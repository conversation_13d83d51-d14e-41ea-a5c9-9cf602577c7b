<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试游戏结果计算</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #2a2a2a;
            border-radius: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            background-color: #3a3a3a;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #1a1a1a;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 游戏结果计算测试</h1>
        
        <button onclick="testCalculateGameResult()">测试 calculateGameResult 方法</button>
        <button onclick="testWithMockData()">使用模拟数据测试</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="test-results"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/game/level.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        let testResults = document.getElementById('test-results');

        function addTestResult(message, isError = false) {
            const div = document.createElement('div');
            div.className = 'test-result' + (isError ? ' error' : '');
            div.innerHTML = message;
            testResults.appendChild(div);
        }

        function clearResults() {
            testResults.innerHTML = '';
        }

        function testCalculateGameResult() {
            addTestResult('<h3>🔍 测试 calculateGameResult 方法</h3>');
            
            try {
                // 创建游戏控制器实例
                const gameController = new GameController();
                
                // 检查方法是否存在
                if (typeof gameController.calculateGameResult === 'function') {
                    addTestResult('✅ calculateGameResult 方法存在');
                    
                    // 尝试调用方法
                    const result = gameController.calculateGameResult();
                    addTestResult('✅ 方法调用成功');
                    addTestResult('<pre>' + JSON.stringify(result, null, 2) + '</pre>');
                    
                } else {
                    addTestResult('❌ calculateGameResult 方法不存在', true);
                }
                
            } catch (error) {
                addTestResult('❌ 测试失败: ' + error.message, true);
                console.error('测试错误:', error);
            }
        }

        function testWithMockData() {
            addTestResult('<h3>🎭 使用模拟数据测试</h3>');
            
            try {
                // 初始化引擎
                if (!window.quantumEngine) {
                    window.quantumEngine = new QuantumEngine();
                }
                if (!window.physicsEngine) {
                    window.physicsEngine = new PhysicsEngine();
                }
                
                // 设置模拟数据
                quantumEngine.score = 1500;
                quantumEngine.combo = 8;
                quantumEngine.level = 2;
                quantumEngine.fieldStrength = 1.5;
                
                // 创建模拟粒子
                physicsEngine.particles = [
                    { id: 'p1', isActive: true, x: 100, y: 100 },
                    { id: 'p2', isActive: false, x: 200, y: 200 },
                    { id: 'p3', isActive: true, x: 300, y: 300 }
                ];
                
                // 创建游戏控制器和模拟关卡
                const gameController = new GameController();
                gameController.currentLevel = {
                    name: '测试关卡',
                    id: 'test-level',
                    difficulty: 'normal',
                    elapsedTime: 45,
                    timeLimit: 60,
                    targetScore: 1000,
                    isCompleted: () => true,
                    isFailed: () => false
                };
                
                // 调用方法
                const result = gameController.calculateGameResult();
                addTestResult('✅ 模拟数据测试成功');
                addTestResult('<pre>' + JSON.stringify(result, null, 2) + '</pre>');
                
                // 验证关键数据
                if (result.score === 1500) {
                    addTestResult('✅ 分数数据正确');
                } else {
                    addTestResult('❌ 分数数据错误: 期望 1500，实际 ' + result.score, true);
                }
                
                if (result.particlesActivated === 2) {
                    addTestResult('✅ 激活粒子数正确');
                } else {
                    addTestResult('❌ 激活粒子数错误: 期望 2，实际 ' + result.particlesActivated, true);
                }
                
                if (result.completed === true) {
                    addTestResult('✅ 完成状态正确');
                } else {
                    addTestResult('❌ 完成状态错误', true);
                }
                
            } catch (error) {
                addTestResult('❌ 模拟数据测试失败: ' + error.message, true);
                console.error('模拟数据测试错误:', error);
            }
        }

        // 页面加载完成后自动运行基本测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                addTestResult('<h2>🚀 自动测试开始</h2>');
                testCalculateGameResult();
            }, 1000);
        });
    </script>
</body>
</html>
