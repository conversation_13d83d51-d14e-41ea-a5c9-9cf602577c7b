<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教学提示系统测试 - 量子共鸣者</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/tutorial.css">
    
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #e0e0e0;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .test-title {
            font-size: 32px;
            color: #00d4ff;
            margin-bottom: 10px;
            text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
        }
        
        .test-description {
            font-size: 16px;
            color: #ccc;
            margin-bottom: 30px;
        }
        
        .test-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 40px;
        }
        
        .test-btn {
            padding: 12px 24px;
            border: 2px solid #00d4ff;
            border-radius: 8px;
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .test-btn:hover {
            background: linear-gradient(135deg, #00e6ff 0%, #00b3e6 100%);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
            transform: translateY(-2px);
        }
        
        .test-btn.secondary {
            background: rgba(255, 255, 255, 0.1);
            color: #e0e0e0;
        }
        
        .test-btn.secondary:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .mock-game-area {
            width: 100%;
            height: 400px;
            background: rgba(0, 0, 0, 0.3);
            border: 2px solid rgba(0, 212, 255, 0.3);
            border-radius: 12px;
            position: relative;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .mock-canvas {
            width: 80%;
            height: 80%;
            background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        
        .mock-particle {
            position: absolute;
            width: 20px;
            height: 20px;
            background: #00d4ff;
            border-radius: 50%;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .mock-particle:hover {
            transform: scale(1.2);
            box-shadow: 0 0 30px rgba(0, 212, 255, 1);
        }
        
        .mock-particle.activated {
            background: #00ff88;
            box-shadow: 0 0 25px rgba(0, 255, 136, 0.8);
        }
        
        .frequency-panel {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .frequency-control {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .frequency-label {
            min-width: 80px;
            font-size: 14px;
            color: #00d4ff;
        }
        
        .frequency-slider {
            flex: 1;
            height: 6px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
            outline: none;
            cursor: pointer;
        }
        
        .frequency-value {
            min-width: 60px;
            text-align: right;
            font-size: 14px;
            color: #e0e0e0;
        }
        
        .score-info {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .score-item {
            text-align: center;
        }
        
        .score-label {
            font-size: 12px;
            color: #888;
            margin-bottom: 5px;
        }
        
        .score-value {
            font-size: 18px;
            font-weight: 600;
            color: #00d4ff;
        }
        
        .log-area {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            height: 200px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            color: #ccc;
        }
        
        .log-entry.info {
            color: #00d4ff;
        }
        
        .log-entry.success {
            color: #00ff88;
        }
        
        .log-entry.warning {
            color: #ffaa00;
        }
        
        .log-entry.error {
            color: #ff4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <div class="test-header">
            <h1 class="test-title">教学提示系统测试</h1>
            <p class="test-description">测试量子共鸣者游戏的教学引导功能</p>
        </div>
        
        <div class="test-controls">
            <button class="test-btn" onclick="startTutorial()">开始教程</button>
            <button class="test-btn secondary" onclick="skipTutorial()">跳过教程</button>
            <button class="test-btn secondary" onclick="resetTest()">重置测试</button>
            <a href="index.html" class="test-btn secondary">返回游戏</a>
        </div>
        
        <!-- 模拟游戏界面 -->
        <div class="mock-game-area" id="game-canvas">
            <div class="mock-canvas">
                <div class="mock-particle" style="top: 30%; left: 30%;" onclick="activateParticle(this, 440)"></div>
                <div class="mock-particle" style="top: 30%; left: 70%;" onclick="activateParticle(this, 880)"></div>
                <div class="mock-particle" style="top: 70%; left: 50%;" onclick="activateParticle(this, 660)"></div>
            </div>
        </div>
        
        <div class="frequency-panel">
            <div class="frequency-control">
                <span class="frequency-label">目标频率:</span>
                <input type="range" class="frequency-slider" min="200" max="2000" value="440" id="frequency-slider">
                <span class="frequency-value" id="frequency-value">440 Hz</span>
            </div>
        </div>
        
        <div class="score-info">
            <div class="score-item">
                <div class="score-label">得分</div>
                <div class="score-value" id="score">0</div>
            </div>
            <div class="score-item">
                <div class="score-label">激活粒子</div>
                <div class="score-value" id="activated">0 / 3</div>
            </div>
            <div class="score-item">
                <div class="score-label">连击</div>
                <div class="score-value" id="combo">0</div>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3 style="color: #00d4ff; margin-bottom: 10px;">系统日志</h3>
            <div class="log-area" id="log-area"></div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/ui/tutorial-system.js"></script>
    
    <script>
        // 模拟游戏状态
        let gameState = {
            score: 0,
            activatedParticles: 0,
            combo: 0,
            targetFrequency: 440
        };
        
        // 日志系统
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // 更新UI
        function updateUI() {
            document.getElementById('score').textContent = gameState.score;
            document.getElementById('activated').textContent = `${gameState.activatedParticles} / 3`;
            document.getElementById('combo').textContent = gameState.combo;
        }
        
        // 频率滑块事件
        document.getElementById('frequency-slider').addEventListener('input', function(e) {
            gameState.targetFrequency = parseInt(e.target.value);
            document.getElementById('frequency-value').textContent = `${gameState.targetFrequency} Hz`;
            log(`目标频率调整为: ${gameState.targetFrequency} Hz`);
        });
        
        // 模拟粒子激活
        function activateParticle(element, frequency) {
            if (element.classList.contains('activated')) {
                log(`粒子已激活 (${frequency} Hz)`, 'warning');
                return;
            }
            
            // 计算共鸣强度
            const resonance = Math.max(0, 1 - Math.abs(frequency - gameState.targetFrequency) / 200);
            
            if (resonance < 0.3) {
                log(`共鸣强度不足: ${resonance.toFixed(2)} (${frequency} Hz)`, 'warning');
                return;
            }
            
            // 激活粒子
            element.classList.add('activated');
            gameState.activatedParticles++;
            gameState.score += Math.round(resonance * 100);
            gameState.combo++;
            
            updateUI();
            log(`粒子激活成功! 频率: ${frequency} Hz, 共鸣强度: ${resonance.toFixed(2)}`, 'success');
            
            // 触发粒子激活事件
            const event = new CustomEvent('particleActivated', {
                detail: {
                    particle: { frequency: frequency },
                    resonanceStrength: resonance,
                    totalActivated: gameState.activatedParticles,
                    score: gameState.score,
                    combo: gameState.combo
                }
            });
            document.dispatchEvent(event);
        }
        
        // 教程控制函数
        function startTutorial() {
            if (window.tutorialSystem) {
                log('开始教程...', 'info');
                window.tutorialSystem.startTutorial('level1');
            } else {
                log('教学提示系统未加载', 'error');
            }
        }
        
        function skipTutorial() {
            if (window.tutorialSystem && window.tutorialSystem.isRunning()) {
                log('跳过教程', 'info');
                window.tutorialSystem.skipTutorial();
            } else {
                log('没有正在运行的教程', 'warning');
            }
        }
        
        function resetTest() {
            // 重置游戏状态
            gameState = {
                score: 0,
                activatedParticles: 0,
                combo: 0,
                targetFrequency: 440
            };
            
            // 重置UI
            document.querySelectorAll('.mock-particle').forEach(p => {
                p.classList.remove('activated');
            });
            document.getElementById('frequency-slider').value = 440;
            document.getElementById('frequency-value').textContent = '440 Hz';
            updateUI();
            
            // 清空日志
            document.getElementById('log-area').innerHTML = '';
            
            log('测试已重置', 'info');
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('教学提示系统测试页面加载完成', 'info');
            
            if (window.tutorialSystem) {
                log('教学提示系统已加载', 'success');
                
                // 设置教程回调
                window.tutorialSystem.on('onStart', (tutorial) => {
                    log(`教程开始: ${tutorial.name}`, 'info');
                });
                
                window.tutorialSystem.on('onComplete', (tutorial) => {
                    log(`教程完成: ${tutorial.name}`, 'success');
                });
                
                window.tutorialSystem.on('onSkip', (tutorial) => {
                    log(`教程跳过: ${tutorial.name}`, 'warning');
                });
                
                window.tutorialSystem.on('onStep', (step, index) => {
                    log(`教程步骤 ${index + 1}: ${step.title}`, 'info');
                });
            } else {
                log('教学提示系统加载失败', 'error');
            }
            
            updateUI();
        });
    </script>
</body>
</html>
