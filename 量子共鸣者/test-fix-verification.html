<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - 修复验证测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #3a3a3a;
            border-radius: 5px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background: #4CAF50;
            color: white;
        }
        .error {
            background: #f44336;
            color: white;
        }
        .warning {
            background: #ff9800;
            color: white;
        }
        .frequency-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 10px 0;
        }
        .frequency-slider {
            flex: 1;
        }
        .frequency-input {
            width: 80px;
        }
        .freq-btn {
            background: #555;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
        }
        .preset-btn {
            background: #666;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            margin: 2px;
        }
        .preset-btn.active {
            background: #4CAF50;
        }
        .hud-display {
            display: flex;
            gap: 20px;
            margin: 10px 0;
        }
        .hud-item {
            background: #444;
            padding: 10px;
            border-radius: 5px;
            min-width: 100px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🧪 量子共鸣者修复验证测试</h1>
        
        <!-- HUD显示测试 -->
        <div class="test-section">
            <h2>📊 HUD显示测试</h2>
            <div class="hud-display">
                <div class="hud-item">
                    <div>分数</div>
                    <div id="current-score">0</div>
                </div>
                <div class="hud-item">
                    <div>连击</div>
                    <div id="current-combo">0</div>
                </div>
                <div class="hud-item">
                    <div>关卡</div>
                    <div id="current-level">1</div>
                </div>
                <div class="hud-item">
                    <div>时间</div>
                    <div id="current-time">00:00</div>
                </div>
            </div>
            <button class="test-button" onclick="testHUDUpdate()">测试HUD更新</button>
            <div id="hud-test-result"></div>
        </div>

        <!-- 频率控制测试 -->
        <div class="test-section">
            <h2>🎚️ 频率控制测试</h2>
            <div class="frequency-controls">
                <label>频率滑块:</label>
                <input type="range" id="frequency-slider" class="frequency-slider" 
                       min="20" max="20000" value="440" step="0.1">
                <input type="number" id="frequency-input" class="frequency-input" 
                       min="20" max="20000" value="440" step="0.1">
                <span id="frequency-value">440.0 Hz</span>
            </div>
            <div>
                <button class="freq-btn" id="freq-down-10">-10Hz</button>
                <button class="freq-btn" id="freq-down-1">-1Hz</button>
                <button class="freq-btn" id="freq-up-1">+1Hz</button>
                <button class="freq-btn" id="freq-up-10">+10Hz</button>
            </div>
            <div>
                <button class="preset-btn" data-freq="220">220Hz</button>
                <button class="preset-btn" data-freq="440">440Hz</button>
                <button class="preset-btn" data-freq="880">880Hz</button>
                <button class="preset-btn" data-freq="1760">1760Hz</button>
            </div>
            <button class="test-button" onclick="testFrequencyControl()">测试频率控制</button>
            <div id="frequency-test-result"></div>
        </div>

        <!-- 游戏数据更新测试 -->
        <div class="test-section">
            <h2>⚙️ 游戏数据更新测试</h2>
            <button class="test-button" onclick="testScoreUpdate()">测试分数更新</button>
            <button class="test-button" onclick="testComboUpdate()">测试连击更新</button>
            <button class="test-button" onclick="testTimeUpdate()">测试时间更新</button>
            <div id="data-test-result"></div>
        </div>

        <!-- 控制台错误检查 -->
        <div class="test-section">
            <h2>🔧 控制台错误检查</h2>
            <button class="test-button" onclick="testConsoleErrors()">检查控制台错误</button>
            <div id="console-test-result"></div>
        </div>

        <!-- 综合测试 -->
        <div class="test-section">
            <h2>🎮 综合功能测试</h2>
            <button class="test-button" onclick="runComprehensiveTest()">运行综合测试</button>
            <div id="comprehensive-test-result"></div>
        </div>
    </div>

    <script>
        // 模拟游戏控制器
        const mockGameController = {
            score: 0,
            combo: 0,
            level: 1,
            gameTime: 0,
            
            updateScoreDisplay(totalScore, scoreIncrease) {
                const scoreElement = document.getElementById('current-score');
                if (scoreElement) {
                    scoreElement.textContent = totalScore.toLocaleString();
                    return true;
                }
                return false;
            },
            
            updateComboDisplay(combo) {
                const comboElement = document.getElementById('current-combo');
                if (comboElement) {
                    comboElement.textContent = combo;
                    return true;
                }
                return false;
            },
            
            updateTimeDisplay() {
                const timeElement = document.getElementById('current-time');
                if (timeElement) {
                    const minutes = Math.floor(this.gameTime / 60);
                    const seconds = Math.floor(this.gameTime % 60);
                    timeElement.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    return true;
                }
                return false;
            }
        };

        // 模拟输入管理器
        const mockInputManager = {
            currentFrequency: 440,
            
            setFrequency(frequency) {
                this.currentFrequency = frequency;
                this.updateFrequencyDisplay(frequency);
            },
            
            updateFrequencyDisplay(frequency) {
                const slider = document.getElementById('frequency-slider');
                const input = document.getElementById('frequency-input');
                const display = document.getElementById('frequency-value');
                
                if (slider) slider.value = frequency;
                if (input) input.value = frequency.toFixed(1);
                if (display) display.textContent = `${frequency.toFixed(1)} Hz`;
            },
            
            adjustFrequency(delta) {
                this.setFrequency(this.currentFrequency + delta);
            }
        };

        // 初始化频率控制
        function initFrequencyControls() {
            // 滑块事件
            const slider = document.getElementById('frequency-slider');
            if (slider) {
                slider.addEventListener('input', (e) => {
                    mockInputManager.setFrequency(parseFloat(e.target.value));
                });
            }

            // 输入框事件
            const input = document.getElementById('frequency-input');
            if (input) {
                input.addEventListener('input', (e) => {
                    const freq = parseFloat(e.target.value);
                    if (!isNaN(freq)) {
                        mockInputManager.setFrequency(freq);
                    }
                });
            }

            // 微调按钮
            document.getElementById('freq-down-10')?.addEventListener('click', () => {
                mockInputManager.adjustFrequency(-10);
            });
            document.getElementById('freq-down-1')?.addEventListener('click', () => {
                mockInputManager.adjustFrequency(-1);
            });
            document.getElementById('freq-up-1')?.addEventListener('click', () => {
                mockInputManager.adjustFrequency(1);
            });
            document.getElementById('freq-up-10')?.addEventListener('click', () => {
                mockInputManager.adjustFrequency(10);
            });

            // 预设按钮
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const freq = parseFloat(btn.dataset.freq);
                    mockInputManager.setFrequency(freq);
                    
                    // 更新按钮状态
                    document.querySelectorAll('.preset-btn').forEach(b => b.classList.remove('active'));
                    btn.classList.add('active');
                });
            });
        }

        // 测试函数
        function testHUDUpdate() {
            const results = [];
            
            // 测试分数更新
            const scoreResult = mockGameController.updateScoreDisplay(12345, 100);
            results.push(scoreResult ? '✅ 分数更新正常' : '❌ 分数更新失败');
            
            // 测试连击更新
            const comboResult = mockGameController.updateComboDisplay(15);
            results.push(comboResult ? '✅ 连击更新正常' : '❌ 连击更新失败');
            
            // 测试时间更新
            mockGameController.gameTime = 125;
            const timeResult = mockGameController.updateTimeDisplay();
            results.push(timeResult ? '✅ 时间更新正常' : '❌ 时间更新失败');
            
            document.getElementById('hud-test-result').innerHTML = 
                `<div class="test-result ${results.every(r => r.includes('✅')) ? 'success' : 'error'}">${results.join('<br>')}</div>`;
        }

        function testFrequencyControl() {
            const results = [];
            
            // 测试频率设置
            mockInputManager.setFrequency(880);
            const slider = document.getElementById('frequency-slider');
            const input = document.getElementById('frequency-input');
            const display = document.getElementById('frequency-value');
            
            results.push(slider && slider.value == '880' ? '✅ 滑块更新正常' : '❌ 滑块更新失败');
            results.push(input && input.value == '880.0' ? '✅ 输入框更新正常' : '❌ 输入框更新失败');
            results.push(display && display.textContent.includes('880.0') ? '✅ 显示更新正常' : '❌ 显示更新失败');
            
            document.getElementById('frequency-test-result').innerHTML = 
                `<div class="test-result ${results.every(r => r.includes('✅')) ? 'success' : 'error'}">${results.join('<br>')}</div>`;
        }

        function testScoreUpdate() {
            mockGameController.updateScoreDisplay(54321, 200);
            document.getElementById('data-test-result').innerHTML = 
                '<div class="test-result success">✅ 分数更新测试完成</div>';
        }

        function testComboUpdate() {
            mockGameController.updateComboDisplay(25);
            document.getElementById('data-test-result').innerHTML = 
                '<div class="test-result success">✅ 连击更新测试完成</div>';
        }

        function testTimeUpdate() {
            mockGameController.gameTime = 185; // 3分5秒
            mockGameController.updateTimeDisplay();
            document.getElementById('data-test-result').innerHTML = 
                '<div class="test-result success">✅ 时间更新测试完成</div>';
        }

        function testConsoleErrors() {
            const results = [];
            
            // 检查关键元素
            const elements = ['current-score', 'current-combo', 'current-level', 'frequency-slider', 'frequency-value'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                results.push(element ? `✅ 元素 #${id} 存在` : `❌ 元素 #${id} 缺失`);
            });
            
            document.getElementById('console-test-result').innerHTML = 
                `<div class="test-result ${results.every(r => r.includes('✅')) ? 'success' : 'error'}">${results.join('<br>')}</div>`;
        }

        function runComprehensiveTest() {
            testHUDUpdate();
            testFrequencyControl();
            testConsoleErrors();
            
            document.getElementById('comprehensive-test-result').innerHTML = 
                '<div class="test-result success">✅ 综合测试完成，请查看各项测试结果</div>';
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', () => {
            initFrequencyControls();
            console.log('🧪 修复验证测试页面已加载');
        });
    </script>
</body>
</html>
