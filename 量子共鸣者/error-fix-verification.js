/**
 * 量子共鸣者 - 错误修复验证脚本
 * 检查和修复常见的JavaScript错误
 */

// 错误修复验证器
class ErrorFixVerifier {
    constructor() {
        this.errors = [];
        this.fixes = [];
        this.warnings = [];
    }

    /**
     * 运行完整的错误检查
     */
    async runFullCheck() {
        console.log('🔍 开始错误修复验证...');
        
        // 检查全局对象
        this.checkGlobalObjects();
        
        // 检查DOM元素
        this.checkDOMElements();
        
        // 检查事件监听器
        this.checkEventListeners();
        
        // 检查游戏状态
        this.checkGameState();
        
        // 检查数据更新
        this.checkDataUpdates();
        
        // 输出结果
        this.outputResults();
    }

    /**
     * 检查全局对象
     */
    checkGlobalObjects() {
        console.log('📋 检查全局对象...');
        
        const requiredObjects = [
            'MathUtils',
            'QuantumEngine', 
            'GameController',
            'InputManager',
            'AudioEngine'
        ];
        
        requiredObjects.forEach(objName => {
            if (typeof window[objName] === 'undefined') {
                this.errors.push(`❌ 全局对象 ${objName} 未定义`);
            } else {
                console.log(`✅ ${objName} 已定义`);
            }
        });

        // 检查实例
        const requiredInstances = [
            'quantumEngine',
            'gameController', 
            'audioEngine'
        ];
        
        requiredInstances.forEach(instanceName => {
            if (typeof window[instanceName] === 'undefined') {
                this.warnings.push(`⚠️ 全局实例 ${instanceName} 未定义`);
            } else {
                console.log(`✅ ${instanceName} 实例已创建`);
            }
        });
    }

    /**
     * 检查DOM元素
     */
    checkDOMElements() {
        console.log('📋 检查DOM元素...');
        
        const requiredElements = [
            'current-score',
            'current-combo', 
            'current-level',
            'frequency-slider',
            'frequency-value',
            'frequency-input',
            'resonance-fill'
        ];
        
        requiredElements.forEach(elementId => {
            const element = document.getElementById(elementId);
            if (!element) {
                this.errors.push(`❌ DOM元素 #${elementId} 未找到`);
            } else {
                console.log(`✅ #${elementId} 元素存在`);
            }
        });
    }

    /**
     * 检查事件监听器
     */
    checkEventListeners() {
        console.log('📋 检查事件监听器...');
        
        // 检查频率滑块事件
        const frequencySlider = document.getElementById('frequency-slider');
        if (frequencySlider) {
            // 模拟事件触发
            try {
                const event = new Event('input');
                frequencySlider.value = 440;
                frequencySlider.dispatchEvent(event);
                console.log('✅ 频率滑块事件正常');
            } catch (error) {
                this.errors.push(`❌ 频率滑块事件错误: ${error.message}`);
            }
        }
    }

    /**
     * 检查游戏状态
     */
    checkGameState() {
        console.log('📋 检查游戏状态...');
        
        if (window.gameController) {
            try {
                // 检查游戏控制器状态
                const isInitialized = gameController.isInitialized;
                console.log(`🎮 游戏控制器初始化状态: ${isInitialized}`);
                
                if (!isInitialized) {
                    this.warnings.push('⚠️ 游戏控制器未完全初始化');
                }
            } catch (error) {
                this.errors.push(`❌ 游戏状态检查错误: ${error.message}`);
            }
        }
    }

    /**
     * 检查数据更新
     */
    checkDataUpdates() {
        console.log('📋 检查数据更新功能...');
        
        if (window.gameController) {
            try {
                // 测试分数更新
                if (typeof gameController.updateScoreDisplay === 'function') {
                    gameController.updateScoreDisplay(1000, 100);
                    console.log('✅ 分数更新功能正常');
                } else {
                    this.errors.push('❌ updateScoreDisplay 方法不存在');
                }
                
                // 测试连击更新
                if (typeof gameController.updateComboDisplay === 'function') {
                    gameController.updateComboDisplay(5);
                    console.log('✅ 连击更新功能正常');
                } else {
                    this.errors.push('❌ updateComboDisplay 方法不存在');
                }
                
                // 测试时间更新
                if (typeof gameController.updateTimeDisplay === 'function') {
                    gameController.gameTime = 65; // 1分5秒
                    gameController.updateTimeDisplay();
                    console.log('✅ 时间更新功能正常');
                } else {
                    this.errors.push('❌ updateTimeDisplay 方法不存在');
                }
                
            } catch (error) {
                this.errors.push(`❌ 数据更新检查错误: ${error.message}`);
            }
        }
    }

    /**
     * 输出检查结果
     */
    outputResults() {
        console.log('\n📊 错误修复验证结果:');
        console.log('='.repeat(50));
        
        if (this.errors.length === 0) {
            console.log('🎉 没有发现严重错误！');
        } else {
            console.log(`❌ 发现 ${this.errors.length} 个错误:`);
            this.errors.forEach(error => console.log(error));
        }
        
        if (this.warnings.length > 0) {
            console.log(`\n⚠️ 发现 ${this.warnings.length} 个警告:`);
            this.warnings.forEach(warning => console.log(warning));
        }
        
        if (this.fixes.length > 0) {
            console.log(`\n🔧 应用了 ${this.fixes.length} 个修复:`);
            this.fixes.forEach(fix => console.log(fix));
        }
        
        console.log('='.repeat(50));
    }

    /**
     * 自动修复常见问题
     */
    autoFix() {
        console.log('🔧 开始自动修复...');
        
        // 修复缺失的全局实例
        if (!window.quantumEngine && window.QuantumEngine) {
            window.quantumEngine = new QuantumEngine();
            this.fixes.push('✅ 创建了 quantumEngine 实例');
        }
        
        if (!window.gameController && window.GameController) {
            window.gameController = new GameController();
            this.fixes.push('✅ 创建了 gameController 实例');
        }
        
        // 修复缺失的DOM元素
        this.fixMissingDOMElements();
        
        console.log('🔧 自动修复完成');
    }

    /**
     * 修复缺失的DOM元素
     */
    fixMissingDOMElements() {
        // 这里可以添加创建缺失DOM元素的逻辑
        // 但通常应该在HTML中正确定义
    }
}

// 创建验证器实例
window.errorFixVerifier = new ErrorFixVerifier();

// 页面加载完成后自动运行检查
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.errorFixVerifier) {
            errorFixVerifier.autoFix();
            errorFixVerifier.runFullCheck();
        }
    }, 2000); // 等待2秒让其他脚本加载完成
});

console.log('🔍 错误修复验证脚本已加载');
