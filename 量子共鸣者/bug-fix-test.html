<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - Bug修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a1a;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #2a2a2a;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #333;
            border-radius: 5px;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log-container {
            background: #000;
            color: #0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 量子共鸣者 - Bug修复测试</h1>
        
        <div class="test-section">
            <h2>🎯 修复的问题</h2>
            <ul>
                <li>✅ 移除了生产环境中的测试弹框</li>
                <li>✅ 修复了 Level 类的方法调用错误</li>
                <li>✅ 解决了 isCompleted/isFailed 方法与属性名冲突问题</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🧪 测试功能</h2>
            <button class="test-button" onclick="testLevelMethods()">测试 Level 类方法</button>
            <button class="test-button" onclick="testGameController()">测试游戏控制器</button>
            <button class="test-button" onclick="checkForTestPopups()">检查测试弹框</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>

        <div class="test-section">
            <h2>📊 测试结果</h2>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h2>📝 控制台日志</h2>
            <div id="console-log" class="log-container"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/game/level.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        // 日志记录功能
        const logContainer = document.getElementById('console-log');
        const resultsContainer = document.getElementById('test-results');
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${timestamp}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            console.log(message);
        }
        
        function addResult(message, type = 'info') {
            const result = document.createElement('div');
            result.className = `test-result ${type}`;
            result.textContent = message;
            resultsContainer.appendChild(result);
        }
        
        function clearLog() {
            logContainer.innerHTML = '';
            resultsContainer.innerHTML = '';
        }

        // 测试 Level 类方法
        function testLevelMethods() {
            log('🧪 开始测试 Level 类方法...');
            
            try {
                // 检查 Level 类是否存在
                if (typeof Level === 'undefined') {
                    addResult('❌ Level 类未找到', 'error');
                    log('❌ Level 类未找到');
                    return;
                }
                
                // 创建测试关卡
                const testConfig = {
                    name: '测试关卡',
                    description: '用于测试的关卡',
                    targetScore: 1000,
                    timeLimit: 60
                };
                
                const level = new Level(testConfig);
                log('✅ Level 实例创建成功');
                
                // 测试 isCompleted 方法
                const completedResult = level.isCompleted();
                if (typeof completedResult === 'boolean') {
                    addResult('✅ isCompleted() 方法正常工作', 'success');
                    log(`✅ isCompleted() 返回: ${completedResult}`);
                } else {
                    addResult('❌ isCompleted() 方法返回值类型错误', 'error');
                    log(`❌ isCompleted() 返回: ${completedResult} (类型: ${typeof completedResult})`);
                }
                
                // 测试 isFailed 方法
                const failedResult = level.isFailed();
                if (typeof failedResult === 'boolean') {
                    addResult('✅ isFailed() 方法正常工作', 'success');
                    log(`✅ isFailed() 返回: ${failedResult}`);
                } else {
                    addResult('❌ isFailed() 方法返回值类型错误', 'error');
                    log(`❌ isFailed() 返回: ${failedResult} (类型: ${typeof failedResult})`);
                }
                
                // 测试状态变更
                level.complete('测试完成');
                const completedAfter = level.isCompleted();
                if (completedAfter === true) {
                    addResult('✅ 关卡完成状态更新正常', 'success');
                    log('✅ 关卡完成状态更新正常');
                } else {
                    addResult('❌ 关卡完成状态更新失败', 'error');
                    log('❌ 关卡完成状态更新失败');
                }
                
            } catch (error) {
                addResult(`❌ Level 类测试失败: ${error.message}`, 'error');
                log(`❌ Level 类测试失败: ${error.message}`);
                console.error(error);
            }
        }

        // 测试游戏控制器
        function testGameController() {
            log('🎮 开始测试游戏控制器...');
            
            try {
                if (typeof GameController === 'undefined') {
                    addResult('⚠️ GameController 类未加载，跳过测试', 'warning');
                    log('⚠️ GameController 类未加载');
                    return;
                }
                
                // 这里可以添加更多游戏控制器测试
                addResult('ℹ️ GameController 测试需要完整的游戏环境', 'warning');
                log('ℹ️ GameController 测试需要完整的游戏环境');
                
            } catch (error) {
                addResult(`❌ GameController 测试失败: ${error.message}`, 'error');
                log(`❌ GameController 测试失败: ${error.message}`);
            }
        }

        // 检查测试弹框
        function checkForTestPopups() {
            log('🔍 检查页面中的测试弹框...');
            
            // 检查是否有测试结果弹框
            const testResults = document.getElementById('test-results');
            const testPopups = document.querySelectorAll('[id*="test"], [class*="test-result"]');
            
            let foundTestPopups = false;
            testPopups.forEach(popup => {
                if (popup.id !== 'test-results' && popup.parentElement !== resultsContainer) {
                    foundTestPopups = true;
                    log(`⚠️ 发现测试弹框: ${popup.id || popup.className}`);
                }
            });
            
            if (!foundTestPopups) {
                addResult('✅ 未发现意外的测试弹框', 'success');
                log('✅ 页面中没有意外的测试弹框');
            } else {
                addResult('⚠️ 发现了一些测试相关元素', 'warning');
            }
            
            // 检查控制台是否有测试脚本的输出
            log('ℹ️ 请检查控制台是否还有 "UI 修复测试结果通过" 等测试输出');
        }

        // 页面加载完成后自动运行基础测试
        window.addEventListener('load', () => {
            log('🚀 页面加载完成，开始自动测试...');
            setTimeout(() => {
                testLevelMethods();
                checkForTestPopups();
            }, 1000);
        });
    </script>
</body>
</html>
