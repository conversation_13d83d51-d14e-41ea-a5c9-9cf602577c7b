<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误修复测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #e0e0e0;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-title {
            font-size: 28px;
            color: #00d4ff;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-section {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-button {
            padding: 12px 24px;
            border: 2px solid #00d4ff;
            border-radius: 8px;
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .test-button:hover {
            background: linear-gradient(135deg, #00e6ff 0%, #00b3e6 100%);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
        }
        
        .log-area {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            color: #ccc;
        }
        
        .log-entry.success {
            color: #00ff88;
        }
        
        .log-entry.error {
            color: #ff4444;
        }
        
        .log-entry.warning {
            color: #ffaa00;
        }
        
        .log-entry.info {
            color: #00d4ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">错误修复测试</h1>
        
        <div class="test-section">
            <h3>测试说明</h3>
            <p>这个测试页面用于验证关卡失败时的错误修复。主要测试以下问题：</p>
            <ul>
                <li>checkAchievements 方法缺少 stats 参数的问题</li>
                <li>checkPerformanceAchievement 方法中 currentStats 为 undefined 的问题</li>
                <li>游戏结束时的统计数据处理</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="testAchievementCheck()">测试成就检查</button>
            <button class="test-button" onclick="testGameEndScenario()">测试游戏结束场景</button>
            <button class="test-button" onclick="testErrorHandling()">测试错误处理</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>测试日志</h3>
            <div class="log-area" id="log-area"></div>
        </div>
    </div>

    <!-- 加载必要的游戏文件 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/game/player-manager.js"></script>
    <script src="js/game/game-controller.js"></script>
    
    <script>
        // 日志系统
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('log-area').innerHTML = '';
        }
        
        // 模拟玩家管理器
        function createMockPlayerManager() {
            if (!window.PlayerManager) {
                log('PlayerManager 类未加载', 'error');
                return null;
            }
            
            const playerManager = new PlayerManager();
            
            // 模拟当前玩家
            playerManager.currentPlayer = {
                id: 'test-player',
                name: '测试玩家',
                stats: {
                    gamesPlayed: 10,
                    totalScore: 5000,
                    bestCombo: 15,
                    chainReactions: 25
                },
                achievements: {}
            };
            
            // 模拟一些成就
            playerManager.achievements = new Map([
                ['combo_master', {
                    id: 'combo_master',
                    name: '连击大师',
                    type: 'performance',
                    condition: { maxCombo: 10 }
                }],
                ['chain_reaction_expert', {
                    id: 'chain_reaction_expert',
                    name: '连锁反应专家',
                    type: 'performance',
                    condition: { chainReactionsInLevel: 5 }
                }]
            ]);
            
            return playerManager;
        }
        
        // 测试成就检查功能
        function testAchievementCheck() {
            log('开始测试成就检查功能...', 'info');
            
            try {
                const playerManager = createMockPlayerManager();
                if (!playerManager) return;
                
                // 测试1: 正常的 stats 对象
                log('测试1: 传入正常的 stats 对象', 'info');
                const normalStats = {
                    maxCombo: 12,
                    chainReactions: 6,
                    accuracy: 0.85
                };
                
                const achievements1 = playerManager.checkAchievements(normalStats);
                log(`正常情况下解锁的成就数量: ${achievements1 ? achievements1.length : 0}`, 'success');
                
                // 测试2: undefined stats
                log('测试2: 传入 undefined stats', 'info');
                const achievements2 = playerManager.checkAchievements(undefined);
                log(`undefined stats 情况下解锁的成就数量: ${achievements2 ? achievements2.length : 0}`, 'success');
                
                // 测试3: 空对象 stats
                log('测试3: 传入空对象 stats', 'info');
                const achievements3 = playerManager.checkAchievements({});
                log(`空对象 stats 情况下解锁的成就数量: ${achievements3 ? achievements3.length : 0}`, 'success');
                
                log('成就检查测试完成', 'success');
                
            } catch (error) {
                log(`成就检查测试失败: ${error.message}`, 'error');
                console.error('Achievement check test error:', error);
            }
        }
        
        // 测试游戏结束场景
        function testGameEndScenario() {
            log('开始测试游戏结束场景...', 'info');
            
            try {
                // 模拟游戏控制器的 updatePlayerStats 方法
                const mockGameResult = {
                    score: 1500,
                    time: 120,
                    maxCombo: 8,
                    chainReactions: 3,
                    particlesActivated: 25,
                    perfectHits: 10,
                    completed: false // 模拟失败情况
                };
                
                log('模拟游戏结果:', 'info');
                log(`- 得分: ${mockGameResult.score}`, 'info');
                log(`- 时间: ${mockGameResult.time}秒`, 'info');
                log(`- 最大连击: ${mockGameResult.maxCombo}`, 'info');
                log(`- 连锁反应: ${mockGameResult.chainReactions}`, 'info');
                log(`- 游戏完成: ${mockGameResult.completed}`, 'info');
                
                // 创建统计对象（模拟 game-controller.js 中的逻辑）
                const stats = {
                    gamesPlayed: 1,
                    totalScore: mockGameResult.score,
                    totalTime: mockGameResult.time,
                    particlesActivated: mockGameResult.particlesActivated || 0,
                    chainReactions: mockGameResult.chainReactions || 0,
                    maxCombo: mockGameResult.maxCombo || 0,
                    perfectHits: mockGameResult.perfectHits || 0
                };
                
                log('创建的统计对象:', 'info');
                log(`- maxCombo: ${stats.maxCombo}`, 'info');
                log(`- chainReactions: ${stats.chainReactions}`, 'info');
                
                // 测试成就检查
                const playerManager = createMockPlayerManager();
                if (playerManager) {
                    const achievements = playerManager.checkAchievements(stats);
                    log(`游戏结束时解锁的成就数量: ${achievements ? achievements.length : 0}`, 'success');
                    
                    if (achievements && achievements.length > 0) {
                        achievements.forEach(achievement => {
                            log(`解锁成就: ${achievement.name}`, 'success');
                        });
                    }
                }
                
                log('游戏结束场景测试完成', 'success');
                
            } catch (error) {
                log(`游戏结束场景测试失败: ${error.message}`, 'error');
                console.error('Game end scenario test error:', error);
            }
        }
        
        // 测试错误处理
        function testErrorHandling() {
            log('开始测试错误处理...', 'info');
            
            try {
                const playerManager = createMockPlayerManager();
                if (!playerManager) return;
                
                // 测试各种边界情况
                const testCases = [
                    { name: 'null stats', stats: null },
                    { name: 'undefined stats', stats: undefined },
                    { name: '空对象 stats', stats: {} },
                    { name: '部分属性缺失', stats: { maxCombo: 5 } },
                    { name: '属性为 null', stats: { maxCombo: null, chainReactions: 3 } },
                    { name: '属性为 undefined', stats: { maxCombo: undefined, chainReactions: undefined } }
                ];
                
                testCases.forEach((testCase, index) => {
                    log(`测试案例 ${index + 1}: ${testCase.name}`, 'info');
                    
                    try {
                        const achievements = playerManager.checkAchievements(testCase.stats);
                        log(`  结果: 成功处理，解锁 ${achievements ? achievements.length : 0} 个成就`, 'success');
                    } catch (error) {
                        log(`  结果: 处理失败 - ${error.message}`, 'error');
                    }
                });
                
                log('错误处理测试完成', 'success');
                
            } catch (error) {
                log(`错误处理测试失败: ${error.message}`, 'error');
                console.error('Error handling test error:', error);
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', async () => {
            log('错误修复测试页面加载完成', 'info');
            log('准备进行修复验证测试...', 'info');

            // 等待存储服务初始化
            if (window.storageService && window.storageService.initPromise) {
                try {
                    await window.storageService.initPromise;
                    log('存储服务初始化完成', 'success');
                } catch (error) {
                    log(`存储服务初始化失败: ${error.message}`, 'warning');
                }
            }

            // 检查必要的类是否加载
            if (window.StorageService) {
                log('StorageService 类已加载', 'success');
            } else {
                log('StorageService 类未加载', 'error');
            }

            if (window.storageService) {
                log('storageService 实例已创建', 'success');
            } else {
                log('storageService 实例未创建', 'error');
            }

            if (window.PlayerManager) {
                log('PlayerManager 类已加载', 'success');
            } else {
                log('PlayerManager 类未加载', 'error');
            }

            if (window.playerManager) {
                log('playerManager 实例已创建', 'success');
            } else {
                log('playerManager 实例未创建', 'error');
            }

            if (window.GameController) {
                log('GameController 类已加载', 'success');
            } else {
                log('GameController 类未加载', 'warning');
            }
        });
    </script>
</body>
</html>
