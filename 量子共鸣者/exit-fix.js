/**
 * 量子共鸣者 - 退出修复脚本
 * 修复页面退出时的错误和资源清理问题
 */

class ExitFix {
    constructor() {
        this.isDestroying = false;
        this.destroyTimeout = null;
    }

    /**
     * 初始化退出修复
     */
    init() {
        console.log('🔧 初始化退出修复...');
        
        // 确保quantumApp有destroy方法
        this.ensureDestroyMethod();
        
        // 重新绑定退出事件
        this.rebindExitEvents();
        
        // 添加额外的安全检查
        this.addSafetyChecks();
        
        console.log('✅ 退出修复初始化完成');
    }

    /**
     * 确保quantumApp有destroy方法
     */
    ensureDestroyMethod() {
        if (window.quantumApp && typeof quantumApp.destroy !== 'function') {
            console.log('🔧 为quantumApp添加destroy方法...');
            
            quantumApp.destroy = function() {
                console.log('🔄 开始销毁应用程序...');
                
                try {
                    // 停止游戏
                    if (window.gameController && gameController.isInitialized) {
                        gameController.destroy();
                    }
                    
                    // 销毁各个系统
                    const systems = [
                        'audioEngine',
                        'audioManager', 
                        'renderEngine',
                        'quantumEngine',
                        'physicsEngine',
                        'playerManager',
                        'screenManager',
                        'uiManager'
                    ];
                    
                    systems.forEach(systemName => {
                        const system = window[systemName];
                        if (system && typeof system.destroy === 'function') {
                            try {
                                system.destroy();
                                console.log(`✅ ${systemName} 已销毁`);
                            } catch (error) {
                                console.warn(`⚠️ ${systemName} 销毁失败:`, error);
                            }
                        }
                    });
                    
                    // 重置应用状态
                    if (this.isInitialized !== undefined) {
                        this.isInitialized = false;
                    }
                    if (this.isLoading !== undefined) {
                        this.isLoading = false;
                    }
                    if (this.appState !== undefined) {
                        this.appState = 'destroyed';
                    }
                    
                    console.log('✅ 应用程序销毁完成');
                    
                } catch (error) {
                    console.error('❌ 应用程序销毁过程中出现错误:', error);
                }
            };
            
            console.log('✅ quantumApp.destroy方法已添加');
        }
    }

    /**
     * 重新绑定退出事件
     */
    rebindExitEvents() {
        // 移除现有的beforeunload监听器
        const existingListeners = window.onbeforeunload;
        window.onbeforeunload = null;
        
        // 添加新的安全退出处理
        window.addEventListener('beforeunload', (event) => {
            this.safeDestroy();
        });
        
        // 添加页面隐藏事件处理
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                // 页面被隐藏时暂停游戏
                if (window.gameController && gameController.gameState === 'playing') {
                    gameController.pauseGame();
                }
            }
        });
        
        // 添加页面卸载事件处理
        window.addEventListener('unload', () => {
            this.safeDestroy();
        });
        
        console.log('✅ 退出事件已重新绑定');
    }

    /**
     * 安全销毁应用程序
     */
    safeDestroy() {
        if (this.isDestroying) {
            console.log('⚠️ 应用程序正在销毁中，跳过重复调用');
            return;
        }
        
        this.isDestroying = true;
        
        // 设置超时保护，防止销毁过程卡住
        this.destroyTimeout = setTimeout(() => {
            console.warn('⚠️ 应用程序销毁超时，强制完成');
            this.isDestroying = false;
        }, 5000);
        
        try {
            console.log('🔄 开始安全销毁应用程序...');
            
            if (window.quantumApp && typeof quantumApp.destroy === 'function') {
                quantumApp.destroy();
            } else {
                console.log('⚠️ quantumApp 或其 destroy 方法不存在，执行备用清理');
                this.fallbackDestroy();
            }
            
            // 清理超时
            if (this.destroyTimeout) {
                clearTimeout(this.destroyTimeout);
                this.destroyTimeout = null;
            }
            
            console.log('✅ 应用程序安全销毁完成');
            
        } catch (error) {
            console.error('❌ 安全销毁过程中出现错误:', error);
        } finally {
            this.isDestroying = false;
        }
    }

    /**
     * 备用销毁方法
     */
    fallbackDestroy() {
        console.log('🔧 执行备用销毁流程...');
        
        // 停止游戏
        if (window.gameController) {
            try {
                gameController.isRunning = false;
                gameController.gameState = 'destroyed';
                console.log('✅ 游戏控制器已停止');
            } catch (error) {
                console.warn('⚠️ 停止游戏控制器失败:', error);
            }
        }
        
        // 停止音频
        if (window.audioEngine) {
            try {
                if (typeof audioEngine.suspend === 'function') {
                    audioEngine.suspend();
                }
                if (typeof audioEngine.clear === 'function') {
                    audioEngine.clear();
                }
                console.log('✅ 音频引擎已停止');
            } catch (error) {
                console.warn('⚠️ 停止音频引擎失败:', error);
            }
        }
        
        // 清理渲染
        if (window.renderEngine) {
            try {
                if (typeof renderEngine.clear === 'function') {
                    renderEngine.clear();
                }
                console.log('✅ 渲染引擎已清理');
            } catch (error) {
                console.warn('⚠️ 清理渲染引擎失败:', error);
            }
        }
        
        console.log('✅ 备用销毁流程完成');
    }

    /**
     * 添加安全检查
     */
    addSafetyChecks() {
        // 全局错误处理
        window.addEventListener('error', (event) => {
            if (event.error && event.error.message && 
                event.error.message.includes('destroy is not a function')) {
                console.warn('⚠️ 检测到destroy方法错误，尝试修复...');
                this.ensureDestroyMethod();
            }
        });
        
        // Promise错误处理
        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && event.reason.message && 
                event.reason.message.includes('destroy is not a function')) {
                console.warn('⚠️ 检测到Promise中的destroy方法错误，尝试修复...');
                this.ensureDestroyMethod();
                event.preventDefault(); // 防止错误弹框
            }
        });
        
        console.log('✅ 安全检查已添加');
    }

    /**
     * 测试销毁功能
     */
    testDestroy() {
        console.log('🧪 测试销毁功能...');
        
        if (window.quantumApp) {
            if (typeof quantumApp.destroy === 'function') {
                console.log('✅ quantumApp.destroy 方法存在');
                
                // 模拟调用（不实际执行）
                console.log('🔍 模拟销毁调用...');
                try {
                    // 这里不实际调用，只是检查方法是否可调用
                    const destroyMethod = quantumApp.destroy;
                    console.log('✅ destroy 方法可以被调用');
                } catch (error) {
                    console.error('❌ destroy 方法调用测试失败:', error);
                }
            } else {
                console.error('❌ quantumApp.destroy 方法不存在');
            }
        } else {
            console.error('❌ quantumApp 对象不存在');
        }
    }
}

// 创建退出修复实例
window.exitFix = new ExitFix();

// 页面加载完成后初始化退出修复
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.exitFix) {
            exitFix.init();
            
            // 延迟测试
            setTimeout(() => {
                exitFix.testDestroy();
            }, 2000);
        }
    }, 1000);
});

console.log('🔧 退出修复脚本已加载');
