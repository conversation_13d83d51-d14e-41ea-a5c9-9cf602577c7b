/**
 * 量子共鸣者 - 快速修复脚本
 * 确保游戏功能正常工作
 */

class QuickFix {
    constructor() {
        this.fixes = [];
        this.errors = [];
    }

    /**
     * 运行所有修复
     */
    async runAllFixes() {
        console.log('🔧 开始快速修复...');
        
        // 等待页面加载
        await this.waitForPageLoad();
        
        // 修复1: 确保全局对象存在
        this.ensureGlobalObjects();
        
        // 修复2: 修复频率控制
        this.fixFrequencyControls();
        
        // 修复3: 修复HUD显示
        this.fixHUDDisplay();
        
        // 修复4: 修复游戏数据更新
        this.fixGameDataUpdates();
        
        // 修复5: 测试所有功能
        this.testAllFunctions();
        
        // 输出修复结果
        this.outputResults();
    }

    /**
     * 等待页面加载
     */
    async waitForPageLoad() {
        return new Promise(resolve => {
            if (document.readyState === 'complete') {
                setTimeout(resolve, 1000);
            } else {
                window.addEventListener('load', () => {
                    setTimeout(resolve, 1000);
                });
            }
        });
    }

    /**
     * 确保全局对象存在
     */
    ensureGlobalObjects() {
        console.log('🔧 检查全局对象...');
        
        // 检查并创建缺失的全局对象
        if (!window.quantumEngine && window.QuantumEngine) {
            try {
                window.quantumEngine = new QuantumEngine();
                this.fixes.push('✅ 创建了 quantumEngine 实例');
            } catch (error) {
                this.errors.push(`❌ 创建 quantumEngine 失败: ${error.message}`);
            }
        }
        
        if (!window.gameController && window.GameController) {
            try {
                window.gameController = new GameController();
                this.fixes.push('✅ 创建了 gameController 实例');
            } catch (error) {
                this.errors.push(`❌ 创建 gameController 失败: ${error.message}`);
            }
        }
    }

    /**
     * 修复频率控制
     */
    fixFrequencyControls() {
        console.log('🔧 修复频率控制...');
        
        try {
            // 检查频率控制元素
            const frequencySlider = document.getElementById('frequency-slider');
            const frequencyInput = document.getElementById('frequency-input');
            const frequencyValue = document.getElementById('frequency-value');
            
            if (!frequencySlider) {
                this.errors.push('❌ 频率滑块元素缺失');
                return;
            }
            
            // 创建简单的频率控制器
            const frequencyController = {
                currentFrequency: 440,
                
                setFrequency(frequency) {
                    this.currentFrequency = Math.max(20, Math.min(20000, frequency));
                    this.updateDisplay();
                },
                
                updateDisplay() {
                    if (frequencySlider) frequencySlider.value = this.currentFrequency;
                    if (frequencyInput) frequencyInput.value = this.currentFrequency.toFixed(1);
                    if (frequencyValue) frequencyValue.textContent = `${this.currentFrequency.toFixed(1)} Hz`;
                },
                
                adjustFrequency(delta) {
                    this.setFrequency(this.currentFrequency + delta);
                }
            };
            
            // 绑定事件
            frequencySlider.addEventListener('input', (e) => {
                frequencyController.setFrequency(parseFloat(e.target.value));
            });
            
            if (frequencyInput) {
                frequencyInput.addEventListener('input', (e) => {
                    const freq = parseFloat(e.target.value);
                    if (!isNaN(freq)) {
                        frequencyController.setFrequency(freq);
                    }
                });
            }
            
            // 微调按钮
            const freqDown10 = document.getElementById('freq-down-10');
            const freqDown1 = document.getElementById('freq-down-1');
            const freqUp1 = document.getElementById('freq-up-1');
            const freqUp10 = document.getElementById('freq-up-10');
            
            if (freqDown10) freqDown10.addEventListener('click', () => frequencyController.adjustFrequency(-10));
            if (freqDown1) freqDown1.addEventListener('click', () => frequencyController.adjustFrequency(-1));
            if (freqUp1) freqUp1.addEventListener('click', () => frequencyController.adjustFrequency(1));
            if (freqUp10) freqUp10.addEventListener('click', () => frequencyController.adjustFrequency(10));
            
            // 预设按钮
            document.querySelectorAll('.preset-btn').forEach(btn => {
                btn.addEventListener('click', () => {
                    const freq = parseFloat(btn.dataset.freq);
                    if (!isNaN(freq)) {
                        frequencyController.setFrequency(freq);
                        
                        // 更新按钮状态
                        document.querySelectorAll('.preset-btn').forEach(b => b.classList.remove('active'));
                        btn.classList.add('active');
                    }
                });
            });
            
            // 保存到全局
            window.frequencyController = frequencyController;
            
            this.fixes.push('✅ 频率控制修复完成');
            
        } catch (error) {
            this.errors.push(`❌ 频率控制修复失败: ${error.message}`);
        }
    }

    /**
     * 修复HUD显示
     */
    fixHUDDisplay() {
        console.log('🔧 修复HUD显示...');
        
        try {
            // 创建HUD控制器
            const hudController = {
                updateScore(score) {
                    const scoreElement = document.getElementById('current-score');
                    if (scoreElement) {
                        scoreElement.textContent = score.toLocaleString();
                        return true;
                    }
                    return false;
                },
                
                updateCombo(combo) {
                    const comboElement = document.getElementById('current-combo');
                    if (comboElement) {
                        comboElement.textContent = combo;
                        return true;
                    }
                    return false;
                },
                
                updateLevel(level) {
                    const levelElement = document.getElementById('current-level');
                    if (levelElement) {
                        levelElement.textContent = level;
                        return true;
                    }
                    return false;
                },
                
                updateTime(seconds) {
                    const timeElement = document.getElementById('current-time');
                    if (timeElement) {
                        const minutes = Math.floor(seconds / 60);
                        const secs = Math.floor(seconds % 60);
                        timeElement.textContent = `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
                        return true;
                    }
                    return false;
                },
                
                updateResonance(strength) {
                    const resonanceFill = document.getElementById('resonance-fill');
                    if (resonanceFill) {
                        const percentage = Math.max(0, Math.min(100, strength * 100));
                        resonanceFill.style.width = `${percentage}%`;
                        return true;
                    }
                    return false;
                }
            };
            
            // 保存到全局
            window.hudController = hudController;
            
            this.fixes.push('✅ HUD显示修复完成');
            
        } catch (error) {
            this.errors.push(`❌ HUD显示修复失败: ${error.message}`);
        }
    }

    /**
     * 修复游戏数据更新
     */
    fixGameDataUpdates() {
        console.log('🔧 修复游戏数据更新...');
        
        try {
            // 如果游戏控制器存在，确保其方法正常工作
            if (window.gameController) {
                // 确保updateScoreDisplay方法存在
                if (typeof gameController.updateScoreDisplay !== 'function') {
                    gameController.updateScoreDisplay = function(totalScore, scoreIncrease) {
                        if (window.hudController) {
                            return hudController.updateScore(totalScore);
                        }
                        return false;
                    };
                }
                
                // 确保updateComboDisplay方法存在
                if (typeof gameController.updateComboDisplay !== 'function') {
                    gameController.updateComboDisplay = function(combo) {
                        if (window.hudController) {
                            return hudController.updateCombo(combo);
                        }
                        return false;
                    };
                }
                
                // 确保updateTimeDisplay方法存在
                if (typeof gameController.updateTimeDisplay !== 'function') {
                    gameController.updateTimeDisplay = function() {
                        if (window.hudController && this.gameTime) {
                            return hudController.updateTime(this.gameTime);
                        }
                        return false;
                    };
                }
            }
            
            this.fixes.push('✅ 游戏数据更新修复完成');
            
        } catch (error) {
            this.errors.push(`❌ 游戏数据更新修复失败: ${error.message}`);
        }
    }

    /**
     * 测试所有功能
     */
    testAllFunctions() {
        console.log('🔧 测试所有功能...');
        
        try {
            // 测试频率控制
            if (window.frequencyController) {
                frequencyController.setFrequency(880);
                this.fixes.push('✅ 频率控制测试通过');
            }
            
            // 测试HUD显示
            if (window.hudController) {
                hudController.updateScore(12345);
                hudController.updateCombo(15);
                hudController.updateLevel(2);
                hudController.updateTime(125);
                hudController.updateResonance(0.75);
                this.fixes.push('✅ HUD显示测试通过');
            }
            
            // 测试游戏控制器方法
            if (window.gameController) {
                if (typeof gameController.updateScoreDisplay === 'function') {
                    gameController.updateScoreDisplay(54321, 100);
                    this.fixes.push('✅ 游戏控制器分数更新测试通过');
                }
                
                if (typeof gameController.updateComboDisplay === 'function') {
                    gameController.updateComboDisplay(25);
                    this.fixes.push('✅ 游戏控制器连击更新测试通过');
                }
            }
            
        } catch (error) {
            this.errors.push(`❌ 功能测试失败: ${error.message}`);
        }
    }

    /**
     * 输出修复结果
     */
    outputResults() {
        console.log('\n🔧 快速修复结果:');
        console.log('='.repeat(50));
        
        if (this.fixes.length > 0) {
            console.log('✅ 成功修复:');
            this.fixes.forEach(fix => console.log(fix));
        }
        
        if (this.errors.length > 0) {
            console.log('\n❌ 修复失败:');
            this.errors.forEach(error => console.log(error));
        }
        
        if (this.errors.length === 0) {
            console.log('\n🎉 所有修复成功完成！');
        } else {
            console.log('\n⚠️ 部分修复失败，请检查错误信息');
        }
        
        console.log('='.repeat(50));
    }
}

// 创建修复实例
window.quickFix = new QuickFix();

// 页面加载完成后自动运行修复
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.quickFix) {
            quickFix.runAllFixes();
        }
    }, 2000); // 等待2秒让其他脚本加载完成
});

console.log('🔧 快速修复脚本已加载');
