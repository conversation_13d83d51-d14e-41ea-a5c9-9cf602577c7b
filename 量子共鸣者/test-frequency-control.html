<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>频率控制测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #2a2a2a;
            border-radius: 10px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #3a3a3a;
            border-radius: 8px;
        }
        .frequency-controls {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 15px 0;
        }
        .frequency-slider {
            flex: 1;
            height: 6px;
            background: #555;
            border-radius: 3px;
            outline: none;
            cursor: pointer;
        }
        .frequency-value {
            min-width: 80px;
            text-align: center;
            font-weight: bold;
            color: #4CAF50;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        .particle-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .particle-card {
            background-color: #4a4a4a;
            padding: 10px;
            border-radius: 5px;
            border-left: 4px solid #2196F3;
        }
        .particle-card.active {
            border-left-color: #4CAF50;
            background-color: #2d5a2d;
        }
        .resonance-info {
            margin: 10px 0;
            padding: 10px;
            background-color: #4a4a4a;
            border-radius: 5px;
        }
        .log {
            background-color: #1a1a1a;
            padding: 10px;
            border-radius: 5px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎚️ 频率控制和粒子激活测试</h1>
        
        <div class="test-section">
            <h3>频率控制</h3>
            <div class="frequency-controls">
                <label>目标频率:</label>
                <input type="range" id="frequency-slider" class="frequency-slider" 
                       min="20" max="2000" value="440" step="1">
                <span class="frequency-value" id="frequency-value">440 Hz</span>
            </div>
            <button onclick="testFrequencySlider()">测试滑块连接</button>
            <button onclick="setRandomFrequency()">随机频率</button>
            <button onclick="setPresetFrequency(220)">220Hz</button>
            <button onclick="setPresetFrequency(440)">440Hz</button>
            <button onclick="setPresetFrequency(880)">880Hz</button>
        </div>

        <div class="test-section">
            <h3>粒子状态</h3>
            <button onclick="createTestParticles()">创建测试粒子</button>
            <button onclick="testParticleActivation()">测试粒子激活</button>
            <button onclick="resetParticles()">重置粒子</button>
            <div class="particle-info" id="particle-info"></div>
        </div>

        <div class="test-section">
            <h3>共鸣计算测试</h3>
            <div class="resonance-info" id="resonance-info">
                <p>调节频率滑块查看共鸣强度变化</p>
            </div>
        </div>

        <div class="test-section">
            <h3>测试日志</h3>
            <div class="log" id="test-log"></div>
            <button onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/game/input-manager.js"></script>

    <script>
        let testParticles = [];
        let currentFrequency = 440;

        function log(message) {
            const logElement = document.getElementById('test-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function clearLog() {
            document.getElementById('test-log').innerHTML = '';
        }

        function updateFrequencyDisplay(frequency) {
            document.getElementById('frequency-value').textContent = `${frequency} Hz`;
            currentFrequency = frequency;
            updateResonanceInfo();
        }

        function testFrequencySlider() {
            log('🎚️ 测试频率滑块连接...');
            
            // 初始化引擎
            if (!window.quantumEngine) {
                window.quantumEngine = new QuantumEngine();
                quantumEngine.init();
                log('✅ 量子引擎初始化完成');
            }

            // 测试滑块事件
            const slider = document.getElementById('frequency-slider');
            if (slider) {
                log('✅ 找到频率滑块');
                
                // 绑定事件
                slider.addEventListener('input', (e) => {
                    const frequency = parseFloat(e.target.value);
                    log(`🎚️ 滑块频率变化: ${frequency} Hz`);
                    updateFrequencyDisplay(frequency);
                    
                    // 更新量子引擎目标频率
                    if (quantumEngine) {
                        quantumEngine.setTargetFrequency(frequency);
                        log(`⚛️ 量子引擎目标频率更新: ${quantumEngine.targetFrequency} Hz`);
                    }
                });
                
                log('✅ 滑块事件绑定完成');
            } else {
                log('❌ 未找到频率滑块');
            }
        }

        function setRandomFrequency() {
            const frequency = Math.floor(Math.random() * 1980) + 20;
            document.getElementById('frequency-slider').value = frequency;
            updateFrequencyDisplay(frequency);
            if (quantumEngine) {
                quantumEngine.setTargetFrequency(frequency);
            }
            log(`🎲 设置随机频率: ${frequency} Hz`);
        }

        function setPresetFrequency(frequency) {
            document.getElementById('frequency-slider').value = frequency;
            updateFrequencyDisplay(frequency);
            if (quantumEngine) {
                quantumEngine.setTargetFrequency(frequency);
            }
            log(`🎯 设置预设频率: ${frequency} Hz`);
        }

        function createTestParticles() {
            log('🔬 创建测试粒子...');
            
            testParticles = [
                { id: 'p1', frequency: 220, x: 100, y: 100, radius: 15, isActive: false },
                { id: 'p2', frequency: 440, x: 200, y: 100, radius: 15, isActive: false },
                { id: 'p3', frequency: 880, x: 300, y: 100, radius: 15, isActive: false },
                { id: 'p4', frequency: 330, x: 150, y: 200, radius: 15, isActive: false },
                { id: 'p5', frequency: 660, x: 250, y: 200, radius: 15, isActive: false }
            ];
            
            log(`✅ 创建了 ${testParticles.length} 个测试粒子`);
            updateParticleDisplay();
        }

        function testParticleActivation() {
            if (testParticles.length === 0) {
                createTestParticles();
            }
            
            if (!quantumEngine) {
                window.quantumEngine = new QuantumEngine();
                quantumEngine.init();
            }
            
            log(`🧪 测试粒子激活，目标频率: ${currentFrequency} Hz`);
            
            testParticles.forEach(particle => {
                if (!particle.isActive) {
                    const resonance = MathUtils.calculateResonance(particle.frequency, currentFrequency, 100);
                    log(`🔍 粒子 ${particle.id} (${particle.frequency}Hz) 共鸣强度: ${resonance.toFixed(3)}`);
                    
                    if (resonance >= quantumEngine.resonanceThreshold) {
                        particle.isActive = true;
                        particle.resonanceStrength = resonance;
                        log(`✅ 粒子 ${particle.id} 激活成功！共鸣强度: ${resonance.toFixed(3)}`);
                    } else {
                        log(`❌ 粒子 ${particle.id} 共鸣强度不足 (需要 >= ${quantumEngine.resonanceThreshold})`);
                    }
                }
            });
            
            updateParticleDisplay();
        }

        function resetParticles() {
            testParticles.forEach(particle => {
                particle.isActive = false;
                particle.resonanceStrength = 0;
            });
            updateParticleDisplay();
            log('🔄 粒子状态已重置');
        }

        function updateParticleDisplay() {
            const container = document.getElementById('particle-info');
            container.innerHTML = '';
            
            testParticles.forEach(particle => {
                const card = document.createElement('div');
                card.className = `particle-card ${particle.isActive ? 'active' : ''}`;
                card.innerHTML = `
                    <strong>${particle.id}</strong><br>
                    频率: ${particle.frequency} Hz<br>
                    状态: ${particle.isActive ? '激活' : '未激活'}<br>
                    ${particle.resonanceStrength ? `共鸣: ${particle.resonanceStrength.toFixed(3)}` : ''}
                `;
                container.appendChild(card);
            });
        }

        function updateResonanceInfo() {
            if (testParticles.length === 0) return;
            
            const container = document.getElementById('resonance-info');
            let html = `<h4>当前频率 ${currentFrequency} Hz 的共鸣分析:</h4>`;
            
            testParticles.forEach(particle => {
                const resonance = MathUtils.calculateResonance(particle.frequency, currentFrequency, 100);
                const canActivate = resonance >= (quantumEngine ? quantumEngine.resonanceThreshold : 0.3);
                html += `
                    <p>
                        ${particle.id} (${particle.frequency}Hz): 
                        共鸣强度 ${resonance.toFixed(3)} 
                        ${canActivate ? '✅ 可激活' : '❌ 无法激活'}
                    </p>
                `;
            });
            
            container.innerHTML = html;
        }

        // 页面加载完成后自动初始化
        window.addEventListener('load', function() {
            log('🚀 页面加载完成，开始初始化测试');
            testFrequencySlider();
            createTestParticles();
            updateResonanceInfo();
        });
    </script>
</body>
</html>
