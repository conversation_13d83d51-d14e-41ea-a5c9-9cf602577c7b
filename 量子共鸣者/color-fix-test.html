<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>颜色修复测试 - 量子共鸣者</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: #0d1421;
            color: white;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 8px;
            border-radius: 4px;
        }
        .success { background: rgba(0, 255, 0, 0.2); }
        .error { background: rgba(255, 0, 0, 0.2); }
        .warning { background: rgba(255, 255, 0, 0.2); }
        canvas {
            border: 1px solid #666;
            margin: 10px 0;
        }
        button {
            background: #6c5ce7;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #5f3dc4;
        }
    </style>
</head>
<body>
    <h1>🎨 颜色处理修复测试</h1>
    
    <div class="test-section">
        <h2>📋 测试概述</h2>
        <p>测试修复后的颜色处理功能，确保不再出现 "hs1" 错误。</p>
        <button onclick="runAllTests()">🚀 运行所有测试</button>
        <button onclick="clearResults()">🧹 清除结果</button>
    </div>

    <div class="test-section">
        <h2>🧪 颜色格式测试</h2>
        <div id="color-format-results"></div>
        <canvas id="color-test-canvas" width="400" height="200"></canvas>
    </div>

    <div class="test-section">
        <h2>🎯 渐变测试</h2>
        <div id="gradient-results"></div>
        <canvas id="gradient-test-canvas" width="400" height="200"></canvas>
    </div>

    <div class="test-section">
        <h2>⚛️ 粒子颜色测试</h2>
        <div id="particle-results"></div>
        <canvas id="particle-test-canvas" width="400" height="200"></canvas>
    </div>

    <div class="test-section">
        <h2>📊 测试结果汇总</h2>
        <div id="summary-results"></div>
    </div>

    <script>
        let testResults = [];

        // 模拟渲染引擎的颜色处理方法
        function addAlphaToColor(color, alpha) {
            try {
                // 处理 HSL 颜色
                if (color.startsWith('hsl(')) {
                    const hslMatch = color.match(/hsl\(([^)]+)\)/);
                    if (hslMatch) {
                        return `hsla(${hslMatch[1]}, ${alpha})`;
                    }
                }
                
                // 处理 RGB 颜色
                if (color.startsWith('rgb(')) {
                    const rgbMatch = color.match(/rgb\(([^)]+)\)/);
                    if (rgbMatch) {
                        return `rgba(${rgbMatch[1]}, ${alpha})`;
                    }
                }
                
                // 处理十六进制颜色
                if (color.startsWith('#')) {
                    const hex = color.slice(1);
                    let r, g, b;
                    
                    if (hex.length === 3) {
                        r = parseInt(hex[0] + hex[0], 16);
                        g = parseInt(hex[1] + hex[1], 16);
                        b = parseInt(hex[2] + hex[2], 16);
                    } else if (hex.length === 6) {
                        r = parseInt(hex.slice(0, 2), 16);
                        g = parseInt(hex.slice(2, 4), 16);
                        b = parseInt(hex.slice(4, 6), 16);
                    } else {
                        throw new Error('无效的十六进制颜色格式');
                    }
                    
                    return `rgba(${r}, ${g}, ${b}, ${alpha})`;
                }
                
                // 如果已经是带透明度的颜色，直接返回
                if (color.includes('rgba') || color.includes('hsla')) {
                    return color;
                }
                
                console.warn('⚠️ 无法识别的颜色格式:', color);
                return color;
                
            } catch (error) {
                console.error('❌ 颜色透明度处理失败:', error, '原颜色:', color);
                return `rgba(255, 255, 255, ${alpha})`;
            }
        }

        // 模拟粒子颜色生成
        function getParticleColor(frequency) {
            const hue = ((frequency - 20) / (20000 - 20)) * 360;
            return `hsl(${hue}, 70%, 60%)`;
        }

        function addResult(section, test, success, message) {
            const result = { section, test, success, message };
            testResults.push(result);
            
            const container = document.getElementById(section + '-results');
            const div = document.createElement('div');
            div.className = `test-result ${success ? 'success' : 'error'}`;
            div.innerHTML = `${success ? '✅' : '❌'} ${test}: ${message}`;
            container.appendChild(div);
        }

        function testColorFormats() {
            console.log('🧪 开始颜色格式测试...');
            
            const testColors = [
                { color: 'hsl(180, 70%, 60%)', name: 'HSL颜色' },
                { color: 'hsl(7.567567567567568, 70%, 60%)', name: 'HSL小数色相' },
                { color: 'rgb(255, 0, 0)', name: 'RGB颜色' },
                { color: '#ff0000', name: '十六进制颜色' },
                { color: '#f00', name: '短十六进制颜色' }
            ];
            
            testColors.forEach(({ color, name }) => {
                try {
                    const result = addAlphaToColor(color, 0.5);
                    const isValid = result.includes('rgba') || result.includes('hsla');
                    addResult('color-format', name, isValid, `${color} → ${result}`);
                } catch (error) {
                    addResult('color-format', name, false, `错误: ${error.message}`);
                }
            });
        }

        function testGradients() {
            console.log('🎯 开始渐变测试...');
            
            const canvas = document.getElementById('gradient-test-canvas');
            const ctx = canvas.getContext('2d');
            
            try {
                // 测试HSL渐变
                const hslColor = 'hsl(7.567567567567568, 70%, 60%)';
                const gradient = ctx.createRadialGradient(100, 100, 0, 100, 100, 50);
                
                gradient.addColorStop(0, hslColor);
                gradient.addColorStop(0.7, addAlphaToColor(hslColor, 0.5));
                gradient.addColorStop(1, 'transparent');
                
                ctx.fillStyle = gradient;
                ctx.fillRect(50, 50, 100, 100);
                
                addResult('gradient', 'HSL渐变', true, '成功创建HSL渐变');
            } catch (error) {
                addResult('gradient', 'HSL渐变', false, `错误: ${error.message}`);
            }
            
            try {
                // 测试RGB渐变
                const rgbColor = 'rgb(255, 107, 107)';
                const gradient2 = ctx.createLinearGradient(200, 50, 300, 150);
                
                gradient2.addColorStop(0, rgbColor);
                gradient2.addColorStop(1, addAlphaToColor(rgbColor, 0.3));
                
                ctx.fillStyle = gradient2;
                ctx.fillRect(200, 50, 100, 100);
                
                addResult('gradient', 'RGB渐变', true, '成功创建RGB渐变');
            } catch (error) {
                addResult('gradient', 'RGB渐变', false, `错误: ${error.message}`);
            }
        }

        function testParticleColors() {
            console.log('⚛️ 开始粒子颜色测试...');
            
            const canvas = document.getElementById('particle-test-canvas');
            const ctx = canvas.getContext('2d');
            
            const frequencies = [440, 880, 1320, 1760, 2200];
            
            frequencies.forEach((freq, index) => {
                try {
                    const color = getParticleColor(freq);
                    const x = 50 + index * 70;
                    const y = 100;
                    
                    // 绘制粒子
                    ctx.fillStyle = color;
                    ctx.beginPath();
                    ctx.arc(x, y, 20, 0, Math.PI * 2);
                    ctx.fill();
                    
                    // 绘制光晕
                    const gradient = ctx.createRadialGradient(x, y, 0, x, y, 30);
                    gradient.addColorStop(0, addAlphaToColor(color, 0.8));
                    gradient.addColorStop(1, addAlphaToColor(color, 0));
                    
                    ctx.fillStyle = gradient;
                    ctx.beginPath();
                    ctx.arc(x, y, 30, 0, Math.PI * 2);
                    ctx.fill();
                    
                    addResult('particle', `频率${freq}Hz`, true, `颜色: ${color}`);
                } catch (error) {
                    addResult('particle', `频率${freq}Hz`, false, `错误: ${error.message}`);
                }
            });
        }

        function runAllTests() {
            clearResults();
            testResults = [];
            
            testColorFormats();
            testGradients();
            testParticleColors();
            
            // 生成汇总
            setTimeout(() => {
                const total = testResults.length;
                const passed = testResults.filter(r => r.success).length;
                const failed = total - passed;
                
                const summaryContainer = document.getElementById('summary-results');
                summaryContainer.innerHTML = `
                    <div class="test-result ${failed === 0 ? 'success' : 'warning'}">
                        📊 测试完成: 总计 ${total} 项，通过 ${passed} 项，失败 ${failed} 项
                    </div>
                    ${failed === 0 ? 
                        '<div class="test-result success">🎉 所有测试通过！颜色处理修复成功。</div>' : 
                        '<div class="test-result error">⚠️ 部分测试失败，需要进一步检查。</div>'
                    }
                `;
            }, 100);
        }

        function clearResults() {
            ['color-format', 'gradient', 'particle', 'summary'].forEach(section => {
                const container = document.getElementById(section + '-results');
                if (container) container.innerHTML = '';
            });
            
            // 清除画布
            ['gradient-test-canvas', 'particle-test-canvas'].forEach(canvasId => {
                const canvas = document.getElementById(canvasId);
                if (canvas) {
                    const ctx = canvas.getContext('2d');
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                }
            });
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            console.log('🚀 颜色修复测试页面已加载');
            setTimeout(runAllTests, 500);
        });
    </script>
</body>
</html>
