<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化错误修复测试 - 量子共鸣者</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #e0e0e0;
            font-family: 'Microsoft YaHei', sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .test-title {
            font-size: 28px;
            color: #00d4ff;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .test-section {
            background: rgba(26, 26, 46, 0.8);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .test-button {
            padding: 12px 24px;
            border: 2px solid #00d4ff;
            border-radius: 8px;
            background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
            color: #fff;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        .test-button:hover {
            background: linear-gradient(135deg, #00e6ff 0%, #00b3e6 100%);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
        }
        
        .log-area {
            background: rgba(0, 0, 0, 0.5);
            border: 1px solid rgba(0, 212, 255, 0.3);
            border-radius: 8px;
            padding: 15px;
            height: 400px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .log-entry {
            margin-bottom: 5px;
            color: #ccc;
        }
        
        .log-entry.success {
            color: #00ff88;
        }
        
        .log-entry.error {
            color: #ff4444;
        }
        
        .log-entry.warning {
            color: #ffaa00;
        }
        
        .log-entry.info {
            color: #00d4ff;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">简化错误修复测试</h1>
        
        <div class="test-section">
            <h3>测试说明</h3>
            <p>这个简化测试页面专门用于验证关卡失败时的错误修复，不依赖复杂的游戏系统。</p>
            <p><strong>修复内容</strong>：</p>
            <ul>
                <li>✅ 修复 checkAchievements 方法缺少 stats 参数的问题</li>
                <li>✅ 添加 checkPerformanceAchievement 方法的空值检查</li>
                <li>✅ 确保游戏结束时不会出现 TypeError</li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>测试控制</h3>
            <button class="test-button" onclick="testParameterPassing()">测试参数传递修复</button>
            <button class="test-button" onclick="testNullHandling()">测试空值处理修复</button>
            <button class="test-button" onclick="testGameEndScenario()">测试游戏结束场景</button>
            <button class="test-button" onclick="clearLog()">清空日志</button>
        </div>
        
        <div class="test-section">
            <h3>测试日志</h3>
            <div class="log-area" id="log-area"></div>
        </div>
    </div>

    <script>
        // 日志系统
        function log(message, type = 'info') {
            const logArea = document.getElementById('log-area');
            const entry = document.createElement('div');
            entry.className = `log-entry ${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // 清空日志
        function clearLog() {
            document.getElementById('log-area').innerHTML = '';
        }
        
        // 模拟 PlayerManager 类的核心方法
        class MockPlayerManager {
            constructor() {
                this.currentPlayer = {
                    id: 'test-player',
                    stats: {
                        gamesPlayed: 10,
                        totalScore: 5000,
                        bestCombo: 15,
                        chainReactions: 25
                    },
                    achievements: {}
                };
                
                this.achievements = new Map([
                    ['combo_master', {
                        id: 'combo_master',
                        name: '连击大师',
                        type: 'performance',
                        condition: { maxCombo: 10 }
                    }],
                    ['chain_reaction_expert', {
                        id: 'chain_reaction_expert',
                        name: '连锁反应专家',
                        type: 'performance',
                        condition: { chainReactionsInLevel: 5 }
                    }]
                ]);
            }
            
            // 修复后的 checkAchievements 方法（接收 stats 参数）
            checkAchievements(stats) {
                if (!this.currentPlayer) return [];
                
                const playerStats = this.currentPlayer.stats;
                const newAchievements = [];
                
                this.achievements.forEach((achievement, achievementId) => {
                    if (this.currentPlayer.achievements[achievementId]) {
                        return;
                    }
                    
                    let unlocked = false;
                    
                    switch (achievement.type) {
                        case 'performance':
                            unlocked = this.checkPerformanceAchievement(achievement, playerStats, stats);
                            break;
                    }
                    
                    if (unlocked) {
                        newAchievements.push(achievement);
                        this.currentPlayer.achievements[achievementId] = {
                            unlockedAt: Date.now(),
                            progress: 1.0
                        };
                    }
                });
                
                return newAchievements;
            }
            
            // 修复后的 checkPerformanceAchievement 方法（添加空值检查）
            checkPerformanceAchievement(achievement, playerStats, currentStats) {
                const condition = achievement.condition;
                
                // 🔧 修复：确保 currentStats 不为空
                if (!currentStats) {
                    currentStats = {};
                }
                
                if (condition.maxCombo) {
                    return Math.max(playerStats.bestCombo || 0, currentStats.maxCombo || 0) >= condition.maxCombo;
                }
                
                if (condition.chainReactionsInLevel) {
                    return (currentStats.chainReactions || 0) >= condition.chainReactionsInLevel;
                }
                
                return false;
            }
        }
        
        // 测试参数传递修复
        function testParameterPassing() {
            log('开始测试参数传递修复...', 'info');
            
            try {
                const playerManager = new MockPlayerManager();
                
                // 模拟修复前的错误调用（不传参数）
                log('测试1: 不传递参数（修复前的错误方式）', 'info');
                try {
                    const achievements1 = playerManager.checkAchievements(); // undefined
                    log(`不传参数时解锁成就数量: ${achievements1.length}`, 'success');
                } catch (error) {
                    log(`不传参数时出错: ${error.message}`, 'error');
                }
                
                // 模拟修复后的正确调用（传递参数）
                log('测试2: 传递正确参数（修复后的正确方式）', 'info');
                const stats = {
                    maxCombo: 12,
                    chainReactions: 6,
                    accuracy: 0.85
                };
                const achievements2 = playerManager.checkAchievements(stats);
                log(`传递参数时解锁成就数量: ${achievements2.length}`, 'success');
                
                log('参数传递修复测试完成', 'success');
                
            } catch (error) {
                log(`参数传递测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试空值处理修复
        function testNullHandling() {
            log('开始测试空值处理修复...', 'info');
            
            try {
                const playerManager = new MockPlayerManager();
                
                const testCases = [
                    { name: 'null stats', stats: null },
                    { name: 'undefined stats', stats: undefined },
                    { name: '空对象 stats', stats: {} },
                    { name: '部分属性缺失', stats: { maxCombo: 5 } },
                    { name: '属性为 null', stats: { maxCombo: null, chainReactions: 3 } }
                ];
                
                testCases.forEach((testCase, index) => {
                    log(`测试案例 ${index + 1}: ${testCase.name}`, 'info');
                    
                    try {
                        const achievements = playerManager.checkAchievements(testCase.stats);
                        log(`  结果: 成功处理，解锁 ${achievements.length} 个成就`, 'success');
                    } catch (error) {
                        log(`  结果: 处理失败 - ${error.message}`, 'error');
                    }
                });
                
                log('空值处理修复测试完成', 'success');
                
            } catch (error) {
                log(`空值处理测试失败: ${error.message}`, 'error');
            }
        }
        
        // 测试游戏结束场景
        function testGameEndScenario() {
            log('开始测试游戏结束场景...', 'info');
            
            try {
                // 模拟游戏失败的统计数据
                const gameResult = {
                    score: 1200,
                    time: 180,
                    maxCombo: 8,
                    chainReactions: 3,
                    particlesActivated: 20,
                    perfectHits: 5,
                    completed: false // 游戏失败
                };
                
                log('模拟游戏失败结果:', 'info');
                log(`- 得分: ${gameResult.score}`, 'info');
                log(`- 时间: ${gameResult.time}秒`, 'info');
                log(`- 最大连击: ${gameResult.maxCombo}`, 'info');
                log(`- 连锁反应: ${gameResult.chainReactions}`, 'info');
                log(`- 游戏完成: ${gameResult.completed}`, 'info');
                
                // 模拟 game-controller.js 中的统计数据创建
                const stats = {
                    gamesPlayed: 1,
                    totalScore: gameResult.score,
                    totalTime: gameResult.time,
                    particlesActivated: gameResult.particlesActivated || 0,
                    chainReactions: gameResult.chainReactions || 0,
                    maxCombo: gameResult.maxCombo || 0,
                    perfectHits: gameResult.perfectHits || 0
                };
                
                log('创建的统计对象:', 'info');
                log(`- maxCombo: ${stats.maxCombo}`, 'info');
                log(`- chainReactions: ${stats.chainReactions}`, 'info');
                
                // 模拟修复后的成就检查调用
                const playerManager = new MockPlayerManager();
                const achievements = playerManager.checkAchievements(stats); // 🔧 修复：传递 stats 参数
                
                log(`游戏结束时解锁的成就数量: ${achievements.length}`, 'success');
                
                if (achievements.length > 0) {
                    achievements.forEach(achievement => {
                        log(`解锁成就: ${achievement.name}`, 'success');
                    });
                } else {
                    log('本次游戏没有解锁新成就', 'info');
                }
                
                log('游戏结束场景测试完成 - 没有出现错误！', 'success');
                
            } catch (error) {
                log(`游戏结束场景测试失败: ${error.message}`, 'error');
                console.error('Game end scenario test error:', error);
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('简化错误修复测试页面加载完成', 'info');
            log('这个测试页面使用模拟的 PlayerManager 类来验证修复效果', 'info');
            log('点击测试按钮开始验证修复...', 'info');
            
            // 显示修复前后的代码对比
            log('', 'info');
            log('=== 修复对比 ===', 'info');
            log('修复前: playerManager.checkAchievements() // 缺少参数', 'warning');
            log('修复后: playerManager.checkAchievements(stats) // 传递统计数据', 'success');
            log('', 'info');
            log('修复前: 没有检查 currentStats 是否为 undefined', 'warning');
            log('修复后: if (!currentStats) { currentStats = {}; }', 'success');
            log('', 'info');
        });
    </script>
</body>
</html>
