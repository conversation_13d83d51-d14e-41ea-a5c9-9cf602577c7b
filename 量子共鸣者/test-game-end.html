<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试游戏结束流程</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #2a2a2a;
            border-radius: 10px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            background-color: #3a3a3a;
            border-radius: 5px;
            border-left: 4px solid #4CAF50;
        }
        .error {
            border-left-color: #f44336;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #1a1a1a;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 游戏结束流程测试</h1>
        
        <button onclick="testGameEndFlow()">测试完整游戏结束流程</button>
        <button onclick="testUpdatePlayerStats()">测试更新玩家统计</button>
        <button onclick="testShowGameOverScreen()">测试显示游戏结束屏幕</button>
        <button onclick="clearResults()">清空结果</button>
        
        <div id="test-results"></div>
    </div>

    <!-- 加载必要的脚本 -->
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/game/level.js"></script>
    <script src="js/game/game-controller.js"></script>

    <script>
        let testResults = document.getElementById('test-results');

        function addTestResult(message, isError = false) {
            const div = document.createElement('div');
            div.className = 'test-result' + (isError ? ' error' : '');
            div.innerHTML = message;
            testResults.appendChild(div);
        }

        function clearResults() {
            testResults.innerHTML = '';
        }

        function setupMockEnvironment() {
            // 初始化引擎
            if (!window.quantumEngine) {
                window.quantumEngine = new QuantumEngine();
            }
            if (!window.physicsEngine) {
                window.physicsEngine = new PhysicsEngine();
            }
            
            // 设置模拟数据
            quantumEngine.score = 1500;
            quantumEngine.combo = 8;
            quantumEngine.maxCombo = 12;
            quantumEngine.level = 2;
            quantumEngine.fieldStrength = 1.5;
            quantumEngine.particlesActivated = 15;
            quantumEngine.chainReactions = 5;
            quantumEngine.perfectHits = 10;
            quantumEngine.totalHits = 15;
            
            // 创建模拟粒子
            physicsEngine.particles = [
                { id: 'p1', isActive: true, x: 100, y: 100 },
                { id: 'p2', isActive: false, x: 200, y: 200 },
                { id: 'p3', isActive: true, x: 300, y: 300 }
            ];
            
            // 模拟音频引擎
            window.audioEngine = {
                suspend: () => console.log('🔇 音频已暂停'),
                isReady: () => true
            };
            
            return true;
        }

        function testGameEndFlow() {
            addTestResult('<h3>🎯 测试完整游戏结束流程</h3>');
            
            try {
                setupMockEnvironment();
                
                // 创建游戏控制器
                const gameController = new GameController();
                gameController.currentLevel = {
                    name: '测试关卡',
                    id: 'test-level',
                    difficulty: 'normal',
                    elapsedTime: 45,
                    timeLimit: 60,
                    targetScore: 1000,
                    isCompleted: () => false,
                    isFailed: () => true
                };
                
                // 模拟游戏结束方法
                gameController.saveHighScore = () => {
                    addTestResult('✅ saveHighScore 调用成功');
                };
                
                gameController.showGameOverScreen = () => {
                    addTestResult('✅ showGameOverScreen 调用成功');
                };
                
                // 调用游戏结束方法
                gameController.endGame();
                
                addTestResult('✅ 游戏结束流程测试完成');
                
            } catch (error) {
                addTestResult('❌ 游戏结束流程测试失败: ' + error.message, true);
                console.error('游戏结束流程测试错误:', error);
            }
        }

        function testUpdatePlayerStats() {
            addTestResult('<h3>📊 测试更新玩家统计</h3>');
            
            try {
                setupMockEnvironment();
                
                // 创建游戏控制器
                const gameController = new GameController();
                gameController.currentLevel = {
                    name: '测试关卡',
                    id: 'test-level',
                    difficulty: 'normal',
                    elapsedTime: 45,
                    timeLimit: 60,
                    targetScore: 1000,
                    isCompleted: () => true,
                    isFailed: () => false
                };
                
                // 模拟玩家管理器
                window.playerManager = {
                    updatePlayerStats: (stats) => {
                        addTestResult('✅ 玩家统计更新成功');
                        addTestResult('<pre>' + JSON.stringify(stats, null, 2) + '</pre>');
                    },
                    updateLevelProgress: (levelId, progress) => {
                        addTestResult('✅ 关卡进度更新成功: ' + levelId);
                    },
                    updateLeaderboard: (type, entry) => {
                        addTestResult('✅ 排行榜更新成功: ' + type);
                    },
                    checkAchievements: () => {
                        addTestResult('✅ 成就检查完成');
                        return [];
                    }
                };
                
                // 调用更新玩家统计方法
                gameController.updatePlayerStats();
                
                addTestResult('✅ 更新玩家统计测试完成');
                
            } catch (error) {
                addTestResult('❌ 更新玩家统计测试失败: ' + error.message, true);
                console.error('更新玩家统计测试错误:', error);
            }
        }

        function testShowGameOverScreen() {
            addTestResult('<h3>🏁 测试显示游戏结束屏幕</h3>');
            
            try {
                setupMockEnvironment();
                
                // 创建游戏控制器
                const gameController = new GameController();
                gameController.currentLevel = {
                    name: '测试关卡',
                    id: 'test-level',
                    difficulty: 'hard',
                    elapsedTime: 30,
                    timeLimit: 60,
                    targetScore: 1000,
                    isCompleted: () => true,
                    isFailed: () => false
                };
                
                // 模拟游戏结束对象
                window.gameOver = {
                    show: (result) => {
                        addTestResult('✅ 游戏结束屏幕显示成功');
                        addTestResult('<pre>' + JSON.stringify(result, null, 2) + '</pre>');
                    }
                };
                
                // 调用显示游戏结束屏幕方法
                gameController.showGameOverScreen();
                
                addTestResult('✅ 显示游戏结束屏幕测试完成');
                
            } catch (error) {
                addTestResult('❌ 显示游戏结束屏幕测试失败: ' + error.message, true);
                console.error('显示游戏结束屏幕测试错误:', error);
            }
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            setTimeout(() => {
                addTestResult('<h2>🚀 自动测试开始</h2>');
                testGameEndFlow();
            }, 1000);
        });
    </script>
</body>
</html>
