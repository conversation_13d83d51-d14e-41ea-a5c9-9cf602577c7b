<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瞬光捕手 - 模块调试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
        }
        .debug-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #333;
            border-radius: 5px;
            background: #16213e;
        }
        .status {
            padding: 5px 10px;
            margin: 5px 0;
            border-radius: 3px;
        }
        .success { background: #4caf50; }
        .error { background: #f44336; }
        .warning { background: #ff9800; }
        .info { background: #2196f3; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>瞬光捕手 - 模块调试</h1>
    
    <div class="debug-section">
        <h2>模块加载状态</h2>
        <div id="module-status"></div>
        <button onclick="checkModuleStatus()">刷新状态</button>
    </div>
    
    <div class="debug-section">
        <h2>初始化测试</h2>
        <div id="init-status"></div>
        <button onclick="testInitialization()">测试初始化</button>
    </div>
    
    <div class="debug-section">
        <h2>错误日志</h2>
        <div id="error-log"></div>
        <button onclick="clearErrorLog()">清除日志</button>
    </div>

    <!-- JavaScript 模块 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/game-engine.js"></script>
    <script src="js/core/player-manager.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/leaderboard-manager.js"></script>
    <script src="js/core/level-editor.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    
    <script>
        // 调试脚本
        const moduleStatusDiv = document.getElementById('module-status');
        const initStatusDiv = document.getElementById('init-status');
        const errorLogDiv = document.getElementById('error-log');
        
        let errorLogs = [];
        
        // 检查模块加载状态
        function checkModuleStatus() {
            const modules = [
                { name: 'storageService', instance: window.storageService },
                { name: 'i18nService', instance: window.i18nService },
                { name: 'gameEngine', instance: window.gameEngine },
                { name: 'playerManager', instance: window.playerManager },
                { name: 'levelManager', instance: window.levelManager },
                { name: 'leaderboardManager', instance: window.leaderboardManager },
                { name: 'levelEditor', instance: window.levelEditor },
                { name: 'screenManager', instance: window.screenManager },
                { name: 'inputHandler', instance: window.inputHandler }
            ];
            
            let html = '';
            modules.forEach(module => {
                const status = module.instance ? 'success' : 'error';
                const statusText = module.instance ? '✅ 已加载' : '❌ 未加载';
                const initStatus = module.instance && module.instance.initialized ? ' (已初始化)' : ' (未初始化)';
                html += `<div class="status ${status}">${module.name}: ${statusText}${module.instance ? initStatus : ''}</div>`;
            });
            
            moduleStatusDiv.innerHTML = html;
        }
        
        // 测试初始化
        async function testInitialization() {
            initStatusDiv.innerHTML = '<div class="status info">开始测试初始化...</div>';
            
            const modules = [
                { name: 'storageService', instance: window.storageService },
                { name: 'i18nService', instance: window.i18nService },
                { name: 'playerManager', instance: window.playerManager },
                { name: 'levelManager', instance: window.levelManager },
                { name: 'leaderboardManager', instance: window.leaderboardManager },
                { name: 'gameEngine', instance: window.gameEngine },
                { name: 'screenManager', instance: window.screenManager },
                { name: 'inputHandler', instance: window.inputHandler }
            ];
            
            for (const module of modules) {
                if (module.instance && typeof module.instance.init === 'function') {
                    try {
                        initStatusDiv.innerHTML += `<div class="status info">正在初始化 ${module.name}...</div>`;
                        await module.instance.init();
                        initStatusDiv.innerHTML += `<div class="status success">✅ ${module.name} 初始化成功</div>`;
                    } catch (error) {
                        const errorMsg = `❌ ${module.name} 初始化失败: ${error.message}`;
                        initStatusDiv.innerHTML += `<div class="status error">${errorMsg}</div>`;
                        addErrorLog(errorMsg, error);
                    }
                } else {
                    initStatusDiv.innerHTML += `<div class="status warning">⚠️ ${module.name} 不存在或没有init方法</div>`;
                }
            }
        }
        
        // 添加错误日志
        function addErrorLog(message, error) {
            errorLogs.push({
                time: new Date().toLocaleTimeString(),
                message: message,
                stack: error ? error.stack : null
            });
            updateErrorLog();
        }
        
        // 更新错误日志显示
        function updateErrorLog() {
            let html = '';
            errorLogs.forEach(log => {
                html += `<div class="status error">
                    <strong>[${log.time}]</strong> ${log.message}
                    ${log.stack ? `<details><summary>堆栈跟踪</summary><pre>${log.stack}</pre></details>` : ''}
                </div>`;
            });
            errorLogDiv.innerHTML = html;
        }
        
        // 清除错误日志
        function clearErrorLog() {
            errorLogs = [];
            errorLogDiv.innerHTML = '';
        }
        
        // 捕获全局错误
        window.addEventListener('error', (event) => {
            addErrorLog(`全局错误: ${event.message}`, event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            addErrorLog(`未处理的Promise拒绝: ${event.reason}`, event.reason);
        });
        
        // 页面加载完成后自动检查状态
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                checkModuleStatus();
            }, 1000);
        });
    </script>
</body>
</html>
