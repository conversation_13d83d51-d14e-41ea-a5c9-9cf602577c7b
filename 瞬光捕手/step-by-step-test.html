<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>逐步测试 - 瞬光捕手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .step {
            background: white;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
        }
        .step.success { border-left-color: #28a745; }
        .step.error { border-left-color: #dc3545; }
        .step.warning { border-left-color: #ffc107; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .disabled { opacity: 0.5; pointer-events: none; }
    </style>
</head>
<body>
    <h1>🔍 瞬光捕手逐步测试</h1>
    
    <div>
        <button onclick="loadNextModule()">加载下一个模块</button>
        <button onclick="testCurrentModule()">测试当前模块</button>
        <button onclick="reset()">重置</button>
    </div>
    
    <div id="test-steps"></div>

    <script>
        const modules = [
            { name: 'storage.js', path: 'js/utils/storage.js', global: 'storageService' },
            { name: 'i18n.js', path: 'js/utils/i18n.js', global: 'i18nService' },
            { name: 'game-engine.js', path: 'js/core/game-engine.js', global: 'gameEngine' },
            { name: 'player-manager.js', path: 'js/core/player-manager.js', global: 'playerManager' },
            { name: 'level-manager.js', path: 'js/core/level-manager.js', global: 'levelManager' },
            { name: 'leaderboard-manager.js', path: 'js/core/leaderboard-manager.js', global: 'leaderboardManager' },
            { name: 'level-editor.js', path: 'js/core/level-editor.js', global: 'levelEditor' },
            { name: 'screen-manager.js', path: 'js/ui/screen-manager.js', global: 'screenManager' },
            { name: 'input-handler.js', path: 'js/ui/input-handler.js', global: 'inputHandler' }
        ];
        
        let currentModuleIndex = 0;
        const testSteps = document.getElementById('test-steps');
        
        function addStep(message, type = 'info') {
            const step = document.createElement('div');
            step.className = `step ${type}`;
            step.innerHTML = `<strong>步骤 ${testSteps.children.length + 1}:</strong> ${message}`;
            testSteps.appendChild(step);
            testSteps.scrollTop = testSteps.scrollHeight;
        }
        
        function loadNextModule() {
            if (currentModuleIndex >= modules.length) {
                addStep('所有模块已加载完成', 'success');
                return;
            }
            
            const module = modules[currentModuleIndex];
            addStep(`正在加载 ${module.name}...`, 'info');
            
            const script = document.createElement('script');
            script.src = module.path;
            script.onload = () => {
                addStep(`✅ ${module.name} 加载成功`, 'success');
                testCurrentModule();
            };
            script.onerror = (error) => {
                addStep(`❌ ${module.name} 加载失败: ${error.message}`, 'error');
            };
            
            document.head.appendChild(script);
            currentModuleIndex++;
        }
        
        function testCurrentModule() {
            if (currentModuleIndex === 0) {
                addStep('还没有加载任何模块', 'warning');
                return;
            }
            
            const module = modules[currentModuleIndex - 1];
            
            if (window[module.global]) {
                addStep(`✅ 全局变量 ${module.global} 已创建`, 'success');
                
                // 测试初始化方法
                if (typeof window[module.global].init === 'function') {
                    addStep(`✅ ${module.global}.init() 方法存在`, 'success');
                    
                    // 特殊处理 screenManager
                    if (module.global === 'screenManager') {
                        addStep('🧪 测试 screenManager 初始化...', 'info');
                        try {
                            const result = window[module.global].init();
                            if (result && typeof result.then === 'function') {
                                result.then(() => {
                                    addStep('✅ screenManager 初始化成功', 'success');
                                }).catch(error => {
                                    addStep(`❌ screenManager 初始化失败: ${error.message}`, 'error');
                                });
                            } else {
                                addStep('✅ screenManager 初始化完成（同步）', 'success');
                            }
                        } catch (error) {
                            addStep(`❌ screenManager 初始化异常: ${error.message}`, 'error');
                        }
                    }
                } else {
                    addStep(`⚠️ ${module.global}.init() 方法不存在`, 'warning');
                }
            } else {
                addStep(`❌ 全局变量 ${module.global} 未创建`, 'error');
            }
        }
        
        function reset() {
            currentModuleIndex = 0;
            testSteps.innerHTML = '';
            
            // 移除已加载的脚本
            const scripts = document.querySelectorAll('script[src^="js/"]');
            scripts.forEach(script => script.remove());
            
            // 清除全局变量
            modules.forEach(module => {
                if (window[module.global]) {
                    delete window[module.global];
                }
            });
            
            addStep('测试已重置', 'info');
        }
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            addStep(`❌ 全局错误: ${event.message} (${event.filename}:${event.lineno})`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            addStep(`❌ 未处理的Promise拒绝: ${event.reason}`, 'error');
        });
        
        // 初始化
        addStep('测试环境已准备就绪，点击"加载下一个模块"开始测试', 'info');
    </script>
</body>
</html>
