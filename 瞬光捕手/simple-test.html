<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单测试 - 瞬光捕手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-result {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
    </style>
</head>
<body>
    <h1>瞬光捕手 - 简单模块测试</h1>
    <div id="test-results"></div>

    <!-- 按照正确顺序加载模块 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/game-engine.js"></script>
    <script src="js/core/player-manager.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/leaderboard-manager.js"></script>
    <script src="js/core/level-editor.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    
    <script>
        function addResult(message, type = 'success') {
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            document.getElementById('test-results').appendChild(div);
        }

        // 等待所有脚本加载完成
        window.addEventListener('load', () => {
            console.log('🧪 开始模块测试...');
            
            // 测试模块是否存在
            const modules = [
                'storageService',
                'i18nService', 
                'gameEngine',
                'playerManager',
                'levelManager',
                'leaderboardManager',
                'levelEditor',
                'screenManager',
                'inputHandler'
            ];
            
            modules.forEach(moduleName => {
                if (window[moduleName]) {
                    addResult(`✅ ${moduleName} 已加载`, 'success');
                    console.log(`✅ ${moduleName}:`, window[moduleName]);
                } else {
                    addResult(`❌ ${moduleName} 未加载`, 'error');
                    console.error(`❌ ${moduleName} 未定义`);
                }
            });
            
            // 测试 screenManager 初始化
            if (window.screenManager) {
                addResult('🧪 测试 screenManager 初始化...', 'warning');
                
                try {
                    screenManager.init().then(() => {
                        addResult('✅ screenManager 初始化成功', 'success');
                    }).catch(error => {
                        addResult(`❌ screenManager 初始化失败: ${error.message}`, 'error');
                        console.error('screenManager 初始化错误:', error);
                    });
                } catch (error) {
                    addResult(`❌ screenManager 初始化异常: ${error.message}`, 'error');
                    console.error('screenManager 初始化异常:', error);
                }
            }
        });
        
        // 捕获全局错误
        window.addEventListener('error', (event) => {
            addResult(`❌ 全局错误: ${event.message}`, 'error');
            console.error('全局错误:', event.error);
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            addResult(`❌ 未处理的Promise拒绝: ${event.reason}`, 'error');
            console.error('未处理的Promise拒绝:', event.reason);
        });
    </script>
</body>
</html>
