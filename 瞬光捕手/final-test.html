<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终测试 - 瞬光捕手</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 5px solid;
        }
        .success { background: rgba(40, 167, 69, 0.2); border-left-color: #28a745; }
        .error { background: rgba(220, 53, 69, 0.2); border-left-color: #dc3545; }
        .warning { background: rgba(255, 193, 7, 0.2); border-left-color: #ffc107; }
        .info { background: rgba(23, 162, 184, 0.2); border-left-color: #17a2b8; }
        button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
            transition: transform 0.2s;
        }
        button:hover { transform: translateY(-2px); }
        .console-output {
            background: rgba(0, 0, 0, 0.3);
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
            margin: 20px 0;
        }
        h1 { text-align: center; margin-bottom: 30px; }
        .emoji { font-size: 1.2em; }
    </style>
</head>
<body>
    <div class="container">
        <h1><span class="emoji">🎮</span> 瞬光捕手 - 最终测试 <span class="emoji">🎮</span></h1>
        
        <div>
            <button onclick="runFullTest()">🚀 运行完整测试</button>
            <button onclick="testGameInit()">🎯 测试游戏初始化</button>
            <button onclick="clearOutput()">🧹 清除输出</button>
        </div>
        
        <div id="test-results"></div>
        
        <h3>📋 控制台输出</h3>
        <div id="console-output" class="console-output"></div>
    </div>

    <!-- 加载所有必需的模块 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/game-engine.js"></script>
    <script src="js/core/player-manager.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/leaderboard-manager.js"></script>
    <script src="js/core/level-editor.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/main.js"></script>

    <script>
        const testResults = document.getElementById('test-results');
        const consoleOutput = document.getElementById('console-output');
        
        function addStatus(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `status ${type}`;
            div.innerHTML = message;
            testResults.appendChild(div);
        }
        
        function addConsoleLog(message) {
            const div = document.createElement('div');
            div.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            consoleOutput.appendChild(div);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearOutput() {
            testResults.innerHTML = '';
            consoleOutput.innerHTML = '';
        }
        
        // 拦截控制台输出
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };
        
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addConsoleLog('LOG: ' + args.join(' '));
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addConsoleLog('ERROR: ' + args.join(' '));
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addConsoleLog('WARN: ' + args.join(' '));
        };
        
        function runFullTest() {
            clearOutput();
            addStatus('<span class="emoji">🔍</span> 开始完整测试...', 'info');
            
            // 测试所有模块是否加载
            const modules = [
                'storageService', 'i18nService', 'gameEngine', 'playerManager',
                'levelManager', 'leaderboardManager', 'levelEditor', 'screenManager', 'inputHandler'
            ];
            
            let allModulesLoaded = true;
            modules.forEach(moduleName => {
                if (window[moduleName]) {
                    addStatus(`<span class="emoji">✅</span> ${moduleName} 已加载`, 'success');
                } else {
                    addStatus(`<span class="emoji">❌</span> ${moduleName} 未加载`, 'error');
                    allModulesLoaded = false;
                }
            });
            
            if (allModulesLoaded) {
                addStatus('<span class="emoji">🎉</span> 所有模块加载成功！', 'success');
            } else {
                addStatus('<span class="emoji">⚠️</span> 部分模块加载失败', 'warning');
            }
            
            // 测试 GameApplication 是否存在
            if (window.GameApplication) {
                addStatus('<span class="emoji">✅</span> GameApplication 类已加载', 'success');
            } else {
                addStatus('<span class="emoji">❌</span> GameApplication 类未加载', 'error');
            }
        }
        
        function testGameInit() {
            addStatus('<span class="emoji">🎯</span> 开始测试游戏初始化...', 'info');
            
            if (!window.GameApplication) {
                addStatus('<span class="emoji">❌</span> GameApplication 未定义，无法测试', 'error');
                return;
            }
            
            try {
                const app = new GameApplication();
                addStatus('<span class="emoji">✅</span> GameApplication 实例创建成功', 'success');
                
                // 测试初始化
                app.init().then(() => {
                    addStatus('<span class="emoji">🎉</span> 游戏初始化成功！原始错误已修复！', 'success');
                }).catch(error => {
                    addStatus(`<span class="emoji">❌</span> 游戏初始化失败: ${error.message}`, 'error');
                });
                
            } catch (error) {
                addStatus(`<span class="emoji">❌</span> 创建 GameApplication 实例失败: ${error.message}`, 'error');
            }
        }
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            addStatus(`<span class="emoji">💥</span> 全局错误: ${event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            addStatus(`<span class="emoji">💥</span> 未处理的Promise拒绝: ${event.reason}`, 'error');
        });
        
        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            setTimeout(() => {
                runFullTest();
                setTimeout(() => {
                    testGameInit();
                }, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
