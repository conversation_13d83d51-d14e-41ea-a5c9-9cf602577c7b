<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>诊断页面 - 瞬光捕手</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background: #1e1e1e;
            color: #ffffff;
        }
        .log-container {
            background: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .log-entry {
            margin: 5px 0;
            padding: 5px;
            border-left: 3px solid #666;
            padding-left: 10px;
        }
        .log-error { border-left-color: #ff4444; color: #ff8888; }
        .log-warn { border-left-color: #ffaa00; color: #ffcc66; }
        .log-info { border-left-color: #4488ff; color: #88ccff; }
        .log-success { border-left-color: #44ff44; color: #88ff88; }
        .timestamp { color: #888; font-size: 0.8em; }
        button {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0088ff; }
    </style>
</head>
<body>
    <h1>🔍 瞬光捕手诊断页面</h1>
    
    <div>
        <button onclick="runDiagnostics()">运行诊断</button>
        <button onclick="clearLogs()">清除日志</button>
        <button onclick="testScreenManager()">测试屏幕管理器</button>
    </div>
    
    <h2>📋 诊断日志</h2>
    <div id="diagnostic-logs" class="log-container"></div>
    
    <h2>🔧 控制台输出</h2>
    <div id="console-output" class="log-container"></div>

    <script>
        // 日志系统
        const diagnosticLogs = document.getElementById('diagnostic-logs');
        const consoleOutput = document.getElementById('console-output');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            diagnosticLogs.appendChild(entry);
            diagnosticLogs.scrollTop = diagnosticLogs.scrollHeight;
        }
        
        function addConsoleLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.innerHTML = `<span class="timestamp">[${timestamp}]</span> ${message}`;
            consoleOutput.appendChild(entry);
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        }
        
        function clearLogs() {
            diagnosticLogs.innerHTML = '';
            consoleOutput.innerHTML = '';
        }
        
        // 拦截控制台输出
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn,
            info: console.info
        };
        
        console.log = function(...args) {
            originalConsole.log.apply(console, args);
            addConsoleLog(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalConsole.error.apply(console, args);
            addConsoleLog(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalConsole.warn.apply(console, args);
            addConsoleLog(args.join(' '), 'warn');
        };
        
        console.info = function(...args) {
            originalConsole.info.apply(console, args);
            addConsoleLog(args.join(' '), 'info');
        };
        
        // 全局错误处理
        window.addEventListener('error', (event) => {
            addLog(`❌ 全局错误: ${event.message} (${event.filename}:${event.lineno})`, 'error');
            addConsoleLog(`❌ 全局错误: ${event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', (event) => {
            addLog(`❌ 未处理的Promise拒绝: ${event.reason}`, 'error');
            addConsoleLog(`❌ 未处理的Promise拒绝: ${event.reason}`, 'error');
        });
        
        // 诊断函数
        function runDiagnostics() {
            addLog('🔍 开始运行诊断...', 'info');
            
            // 检查模块加载状态
            const modules = [
                'storageService', 'i18nService', 'gameEngine', 'playerManager',
                'levelManager', 'leaderboardManager', 'levelEditor', 'screenManager', 'inputHandler'
            ];
            
            modules.forEach(moduleName => {
                if (window[moduleName]) {
                    addLog(`✅ ${moduleName} 已加载`, 'success');
                } else {
                    addLog(`❌ ${moduleName} 未加载`, 'error');
                }
            });
            
            // 检查DOM元素
            const requiredElements = ['loading-screen', 'main-menu', 'game-screen'];
            requiredElements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    addLog(`✅ DOM元素 #${elementId} 存在`, 'success');
                } else {
                    addLog(`❌ DOM元素 #${elementId} 不存在`, 'error');
                }
            });
            
            addLog('🔍 诊断完成', 'info');
        }
        
        function testScreenManager() {
            addLog('🧪 开始测试屏幕管理器...', 'info');
            
            if (!window.screenManager) {
                addLog('❌ screenManager 未定义', 'error');
                return;
            }
            
            addLog('✅ screenManager 已定义', 'success');
            addLog(`📊 screenManager 类型: ${typeof screenManager}`, 'info');
            addLog(`📊 screenManager.init 类型: ${typeof screenManager.init}`, 'info');
            
            if (typeof screenManager.init === 'function') {
                addLog('🚀 尝试初始化 screenManager...', 'info');
                try {
                    const result = screenManager.init();
                    if (result && typeof result.then === 'function') {
                        result.then(() => {
                            addLog('✅ screenManager 初始化成功', 'success');
                        }).catch(error => {
                            addLog(`❌ screenManager 初始化失败: ${error.message}`, 'error');
                        });
                    } else {
                        addLog('✅ screenManager 初始化完成（同步）', 'success');
                    }
                } catch (error) {
                    addLog(`❌ screenManager 初始化异常: ${error.message}`, 'error');
                }
            } else {
                addLog('❌ screenManager.init 不是函数', 'error');
            }
        }
        
        // 页面加载完成后自动运行诊断
        window.addEventListener('load', () => {
            setTimeout(() => {
                runDiagnostics();
            }, 1000);
        });
    </script>
    
    <!-- 按照正确顺序加载模块 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/core/game-engine.js"></script>
    <script src="js/core/player-manager.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/leaderboard-manager.js"></script>
    <script src="js/core/level-editor.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
</body>
</html>
